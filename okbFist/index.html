<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title id="pageTitle">burn</title>
    <link rel="icon" type="image/svg+xml" href="logo.png" />
    <link rel="alternate icon" href="favicon.ico" />
    <link rel="apple-touch-icon" href="logo.png" />
    <script src="https://cdn.jsdelivr.net/npm/web3@1.10.0/dist/web3.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont,
          "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 20% 80%,
            rgba(120, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(120, 219, 255, 0.2) 0%,
            transparent 50%
          );
        background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
        -webkit-background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
        background-attachment: fixed;
        animation: gradientShift 15s ease infinite;
        -webkit-animation: gradientShift 15s ease infinite;
        color: #ffffff;
        min-height: 100vh;
        padding: 0;
        margin: 0;
        position: relative;
        overflow-x: hidden;
        line-height: 1.7;
      }

      @keyframes gradientShift {
        0% {
          background-position: 0% 50%, 0% 0%, 0% 0%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%, 0% 0%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%, 0% 0%, 0% 0%;
        }
      }

      /* Glass morphism overlay */
      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 25% 25%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 75% 75%,
            rgba(255, 255, 255, 0.05) 0%,
            transparent 50%
          );
        backdrop-filter: blur(0.5px);
        pointer-events: none;
        z-index: -1;
      }

      /* Floating geometric shapes */
      body::after {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: radial-gradient(
            circle at 20% 20%,
            rgba(255, 255, 255, 0.1) 2px,
            transparent 2px
          ),
          radial-gradient(
            circle at 80% 80%,
            rgba(255, 255, 255, 0.08) 1px,
            transparent 1px
          ),
          radial-gradient(
            circle at 40% 60%,
            rgba(255, 255, 255, 0.06) 1px,
            transparent 1px
          );
        background-size: 100px 100px, 150px 150px, 200px 200px;
        animation: floatShapes 25s linear infinite;
        pointer-events: none;
        z-index: -1;
      }

      @keyframes floatShapes {
        0% {
          transform: translateY(0px) rotate(0deg);
        }
        100% {
          transform: translateY(-100px) rotate(360deg);
        }
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
        padding: 30px 20px;
      }

      /* Decorative elements */
      .container::before {
        content: "";
        position: absolute;
        top: 10%;
        right: -10%;
        width: 300px;
        height: 300px;
        background: radial-gradient(
          circle,
          rgba(255, 107, 53, 0.1) 0%,
          transparent 70%
        );
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        pointer-events: none;
      }

      .container::after {
        content: "";
        position: absolute;
        bottom: 20%;
        left: -10%;
        width: 200px;
        height: 200px;
        background: radial-gradient(
          circle,
          rgba(0, 212, 170, 0.1) 0%,
          transparent 70%
        );
        border-radius: 50%;
        animation: float 8s ease-in-out infinite reverse;
        pointer-events: none;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }

      /* Fire Video Background */
      .fire-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
        overflow: hidden;
      }

      .fire-video {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 30%;
        object-fit: cover;
        opacity: 0.8;
        filter: hue-rotate(10deg) saturate(1.2);
      }

      @keyframes fireGlow {
        0% {
          opacity: 0.6;
          transform: scale(1) translateY(0px);
        }
        50% {
          opacity: 0.9;
          transform: scale(1.02) translateY(-5px);
        }
        100% {
          opacity: 0.7;
          transform: scale(1.01) translateY(-2px);
        }
      }

      /* Floating embers */
      .ember {
        position: absolute;
        width: 4px;
        height: 4px;
        background: #ff6b35;
        border-radius: 50%;
        animation: float 4s infinite linear;
        opacity: 0.8;
        will-change: transform, opacity;
        transform: translateZ(0);
      }

      .ember:nth-child(10) {
        left: 15%;
        animation-delay: 0s;
      }
      .ember:nth-child(11) {
        left: 35%;
        animation-delay: 1s;
      }
      .ember:nth-child(12) {
        left: 55%;
        animation-delay: 2s;
      }
      .ember:nth-child(13) {
        left: 75%;
        animation-delay: 3s;
      }
      .ember:nth-child(14) {
        left: 25%;
        animation-delay: 0.5s;
      }
      .ember:nth-child(15) {
        left: 65%;
        animation-delay: 1.5s;
      }
      .ember:nth-child(16) {
        left: 45%;
        animation-delay: 2.5s;
      }
      .ember:nth-child(17) {
        left: 85%;
        animation-delay: 0.8s;
      }
      .ember:nth-child(18) {
        left: 5%;
        animation-delay: 1.8s;
      }
      .ember:nth-child(19) {
        left: 95%;
        animation-delay: 3.2s;
      }
      .ember:nth-child(20) {
        left: 12%;
        animation-delay: 0.3s;
      }
      .ember:nth-child(21) {
        left: 32%;
        animation-delay: 1.3s;
      }
      .ember:nth-child(22) {
        left: 52%;
        animation-delay: 2.3s;
      }
      .ember:nth-child(23) {
        left: 72%;
        animation-delay: 3.3s;
      }
      .ember:nth-child(24) {
        left: 22%;
        animation-delay: 0.7s;
      }
      .ember:nth-child(25) {
        left: 62%;
        animation-delay: 1.7s;
      }
      .ember:nth-child(26) {
        left: 42%;
        animation-delay: 2.7s;
      }
      .ember:nth-child(27) {
        left: 82%;
        animation-delay: 3.7s;
      }
      .ember:nth-child(28) {
        left: 8%;
        animation-delay: 0.2s;
      }
      .ember:nth-child(29) {
        left: 88%;
        animation-delay: 1.2s;
      }
      .ember:nth-child(30) {
        left: 18%;
        animation-delay: 2.2s;
      }
      .ember:nth-child(31) {
        left: 38%;
        animation-delay: 3.8s;
      }
      .ember:nth-child(32) {
        left: 58%;
        animation-delay: 0.4s;
      }
      .ember:nth-child(33) {
        left: 78%;
        animation-delay: 1.4s;
      }
      .ember:nth-child(34) {
        left: 28%;
        animation-delay: 2.8s;
      }
      .ember:nth-child(35) {
        left: 48%;
        animation-delay: 0.6s;
      }
      .ember:nth-child(36) {
        left: 68%;
        animation-delay: 1.6s;
      }
      .ember:nth-child(37) {
        left: 13%;
        animation-delay: 2.6s;
      }
      .ember:nth-child(38) {
        left: 33%;
        animation-delay: 3.6s;
      }
      .ember:nth-child(39) {
        left: 53%;
        animation-delay: 0.9s;
      }
      .ember:nth-child(40) {
        left: 73%;
        animation-delay: 1.9s;
      }
      .ember:nth-child(41) {
        left: 93%;
        animation-delay: 2.9s;
      }
      .ember:nth-child(42) {
        left: 3%;
        animation-delay: 3.9s;
      }
      .ember:nth-child(43) {
        left: 23%;
        animation-delay: 0.1s;
      }
      .ember:nth-child(44) {
        left: 43%;
        animation-delay: 1.1s;
      }
      .ember:nth-child(45) {
        left: 63%;
        animation-delay: 2.1s;
      }
      .ember:nth-child(46) {
        left: 83%;
        animation-delay: 3.1s;
      }
      .ember:nth-child(47) {
        left: 16%;
        animation-delay: 0.35s;
      }
      .ember:nth-child(48) {
        left: 36%;
        animation-delay: 1.35s;
      }
      .ember:nth-child(49) {
        left: 56%;
        animation-delay: 2.35s;
      }
      .ember:nth-child(50) {
        left: 76%;
        animation-delay: 3.35s;
      }
      .ember:nth-child(51) {
        left: 14%;
        animation-delay: 0.15s;
      }
      .ember:nth-child(52) {
        left: 34%;
        animation-delay: 1.15s;
      }
      .ember:nth-child(53) {
        left: 54%;
        animation-delay: 2.15s;
      }
      .ember:nth-child(54) {
        left: 74%;
        animation-delay: 3.15s;
      }
      .ember:nth-child(55) {
        left: 94%;
        animation-delay: 0.25s;
      }
      .ember:nth-child(56) {
        left: 4%;
        animation-delay: 1.25s;
      }
      .ember:nth-child(57) {
        left: 24%;
        animation-delay: 2.25s;
      }
      .ember:nth-child(58) {
        left: 44%;
        animation-delay: 3.25s;
      }
      .ember:nth-child(59) {
        left: 64%;
        animation-delay: 0.45s;
      }
      .ember:nth-child(60) {
        left: 84%;
        animation-delay: 1.45s;
      }
      .ember:nth-child(61) {
        left: 11%;
        animation-delay: 2.45s;
      }
      .ember:nth-child(62) {
        left: 31%;
        animation-delay: 3.45s;
      }
      .ember:nth-child(63) {
        left: 51%;
        animation-delay: 0.55s;
      }
      .ember:nth-child(64) {
        left: 71%;
        animation-delay: 1.55s;
      }
      .ember:nth-child(65) {
        left: 91%;
        animation-delay: 2.55s;
      }
      .ember:nth-child(66) {
        left: 1%;
        animation-delay: 3.55s;
      }
      .ember:nth-child(67) {
        left: 21%;
        animation-delay: 0.65s;
      }
      .ember:nth-child(68) {
        left: 41%;
        animation-delay: 1.65s;
      }
      .ember:nth-child(69) {
        left: 61%;
        animation-delay: 2.65s;
      }
      .ember:nth-child(70) {
        left: 81%;
        animation-delay: 3.65s;
      }
      .ember:nth-child(71) {
        left: 17%;
        animation-delay: 0.75s;
      }
      .ember:nth-child(72) {
        left: 37%;
        animation-delay: 1.75s;
      }
      .ember:nth-child(73) {
        left: 57%;
        animation-delay: 2.75s;
      }
      .ember:nth-child(74) {
        left: 77%;
        animation-delay: 3.75s;
      }
      .ember:nth-child(75) {
        left: 97%;
        animation-delay: 0.85s;
      }
      .ember:nth-child(76) {
        left: 7%;
        animation-delay: 1.85s;
      }
      .ember:nth-child(77) {
        left: 27%;
        animation-delay: 2.85s;
      }
      .ember:nth-child(78) {
        left: 47%;
        animation-delay: 3.85s;
      }
      .ember:nth-child(79) {
        left: 67%;
        animation-delay: 0.95s;
      }
      .ember:nth-child(80) {
        left: 87%;
        animation-delay: 1.95s;
      }
      .ember:nth-child(81) {
        left: 15%;
        animation-delay: 2.95s;
      }
      .ember:nth-child(82) {
        left: 35%;
        animation-delay: 3.95s;
      }
      .ember:nth-child(83) {
        left: 55%;
        animation-delay: 0.05s;
      }
      .ember:nth-child(84) {
        left: 75%;
        animation-delay: 1.05s;
      }
      .ember:nth-child(85) {
        left: 95%;
        animation-delay: 2.05s;
      }
      .ember:nth-child(86) {
        left: 9%;
        animation-delay: 3.05s;
      }
      .ember:nth-child(87) {
        left: 29%;
        animation-delay: 0.12s;
      }
      .ember:nth-child(88) {
        left: 49%;
        animation-delay: 1.12s;
      }
      .ember:nth-child(89) {
        left: 69%;
        animation-delay: 2.12s;
      }

      @keyframes float {
        0% {
          bottom: -10px;
          opacity: 1;
          transform: translateX(0px);
        }
        25% {
          opacity: 0.8;
          transform: translateX(10px);
        }
        50% {
          opacity: 0.6;
          transform: translateX(-5px);
        }
        75% {
          opacity: 0.4;
          transform: translateX(15px);
        }
        100% {
          bottom: 100vh;
          opacity: 0;
          transform: translateX(-10px);
        }
      }

      /* Heat wave effect */
      .heat-wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 200px;
        background: linear-gradient(
          to top,
          rgba(255, 107, 53, 0.1) 0%,
          rgba(247, 147, 30, 0.05) 50%,
          transparent 100%
        );
        animation: wave 3s ease-in-out infinite;
      }

      @keyframes wave {
        0%,
        100% {
          transform: scaleY(1) skewX(0deg);
          opacity: 0.1;
        }
        50% {
          transform: scaleY(1.1) skewX(1deg);
          opacity: 0.2;
        }
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 50px;
        padding: 25px 40px;
        background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.25) 0%,
            rgba(255, 255, 255, 0.1) 100%
          ),
          linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(118, 75, 162, 0.1) 100%
          );
        border-radius: 30px;
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: headerShine 4s ease-in-out infinite;
      }

      @keyframes headerShine {
        0% {
          left: -100%;
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
        100% {
          left: 100%;
          opacity: 0;
        }
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 36px;
        font-weight: 900;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: logoGradient 3s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.3));
      }

      @keyframes logoGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .logo-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.3) 0%,
            transparent 50%
          );
        background-size: 200% 200%, 100% 100%;
        animation: logoIconGradient 4s ease infinite;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4),
          0 0 0 2px rgba(255, 255, 255, 0.1),
          inset 0 2px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
      }

      @keyframes logoIconGradient {
        0% {
          background-position: 0% 50%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%;
        }
      }

      .logo-icon::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
      }

      .logo-icon:hover {
        transform: scale(1.1) rotate(-5deg);
        box-shadow: 0 20px 50px rgba(102, 126, 234, 0.5),
          0 0 0 2px rgba(255, 255, 255, 0.2),
          inset 0 2px 0 rgba(255, 255, 255, 0.4);
      }

      .logo-icon:hover::before {
        opacity: 1;
        transform: rotate(45deg) translate(50%, 50%);
      }

      .header-controls {
        display: flex;
        align-items: center;
        gap: 25px;
        position: relative;
        z-index: 2;
      }

      /* Language Switch Styles */
      .language-switch {
        display: flex;
        background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%
          ),
          linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(118, 75, 162, 0.1) 100%
          );
        border-radius: 25px;
        padding: 4px;
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
      }

      .language-switch::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .language-switch:hover::before {
        opacity: 1;
      }

      .lang-btn {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.9);
        padding: 12px 20px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 700;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        min-width: 60px;
        position: relative;
        z-index: 1;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .lang-btn:hover {
        color: white;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
      }

      .lang-btn.active {
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 50%
          );
        background-size: 200% 200%, 100% 100%;
        animation: langBtnGradient 3s ease infinite;
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      @keyframes langBtnGradient {
        0% {
          background-position: 0% 50%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%;
        }
      }

      .lang-btn.active::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        border-radius: 20px;
        animation: activeGlow 2s ease-in-out infinite alternate;
      }

      @keyframes activeGlow {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      .wallet-address {
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.2) 0%,
          rgba(255, 142, 83, 0.1) 100%
        );
        padding: 14px 28px;
        border-radius: 30px;
        border: 1px solid rgba(255, 107, 53, 0.3);
        font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono",
          monospace;
        backdrop-filter: blur(15px);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      .wallet-address::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transition: left 0.6s ease;
      }

      .wallet-address:hover {
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.3) 0%,
          rgba(255, 142, 83, 0.2) 100%
        );
        border-color: rgba(255, 107, 53, 0.5);
        box-shadow: 0 12px 35px rgba(255, 107, 53, 0.25),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .wallet-address:hover::before {
        left: 100%;
      }

      .card {
        background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.05) 100%
          ),
          linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(118, 75, 162, 0.05) 100%
          );
        border-radius: 30px;
        padding: 45px;
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 2px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      }

      .card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
      }

      .card::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 20% 20%,
            rgba(102, 126, 234, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 80%,
            rgba(240, 147, 251, 0.1) 0%,
            transparent 50%
          );
        opacity: 0;
        transition: opacity 0.4s ease;
        pointer-events: none;
      }

      .card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 35px 80px rgba(0, 0, 0, 0.15),
          0 0 0 2px rgba(255, 255, 255, 0.2),
          inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .card:hover::after {
        opacity: 1;
      }

      .card-title {
        font-size: 36px;
        font-weight: 900;
        margin-bottom: 40px;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: titleGradient 4s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.2));
      }

      @keyframes titleGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .card-title::after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 0;
        width: 80px;
        height: 4px;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: titleUnderlineGradient 3s ease infinite;
        border-radius: 2px;
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
      }

      @keyframes titleUnderlineGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      /* Common gradient animations */
      @keyframes btnGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes textGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      /* Switch Toggle Styles */
      .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 34px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1),
          0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 3px;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.1);
      }

      input:checked + .slider {
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: switchGradient 3s ease infinite;
        border-color: rgba(102, 126, 234, 0.4);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1),
          0 2px 8px rgba(102, 126, 234, 0.3), 0 0 20px rgba(102, 126, 234, 0.2);
      }

      @keyframes switchGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      input:checked + .slider:before {
        transform: translateX(26px);
        background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3),
          0 0 0 1px rgba(255, 255, 255, 0.2);
      }

      .slider:hover {
        border-color: rgba(255, 255, 255, 0.3);
      }

      input:checked + .slider:hover {
        border-color: rgba(102, 126, 234, 0.6);
      }

      /* Global Social Media Section */
      .global-social-section {
        max-width: 1400px;
        margin: 50px auto 20px auto;
        padding: 0 20px;
        position: relative;
        z-index: 1;
      }

      .global-social-section .social-card {
        background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.05) 100%
          ),
          linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(118, 75, 162, 0.05) 100%
          );
        border-radius: 30px;
        padding: 45px;
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 2px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      }

      .global-social-section .social-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transition: left 0.6s ease;
      }

      .global-social-section .social-card:hover::before {
        left: 100%;
      }

      .global-social-section .social-section {
        text-align: center;
      }

      .global-social-section .social-title {
        font-size: 28px;
        font-weight: 800;
        margin-bottom: 30px;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: titleGradient 4s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.2));
      }

      .global-social-section .social-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 30px;
        flex-wrap: wrap;
      }

      .wallet-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
      }

      .info-item {
        background: rgba(255, 255, 255, 0.05);
        padding: 20px;
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .info-label {
        font-size: 14px;
        color: #888;
        margin-bottom: 8px;
      }

      .info-value {
        font-size: 18px;
        font-weight: bold;
        font-family: monospace;
      }

      .progress-section {
        margin-bottom: 30px;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .progress-label {
        font-size: 16px;
        color: #ccc;
      }

      .progress-value {
        font-size: 16px;
        font-weight: bold;
      }

      .progress-bar {
        width: 100%;
        height: 16px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2),
          0 1px 0 rgba(255, 255, 255, 0.1);
        position: relative;
      }

      .progress-bar::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(
          135deg,
          #ff6b35 0%,
          #ff8e53 50%,
          #ffa726 100%
        );
        border-radius: 12px;
        transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .progress-fill::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        animation: progressShine 2s infinite;
      }

      @keyframes progressShine {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      .progress-percentage {
        text-align: right;
        font-size: 16px;
        color: #ff6b35;
        font-weight: 700;
        text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        margin-top: 5px;
      }

      .buy-section {
        margin-bottom: 30px;
      }

      .amount-input {
        display: flex;
        align-items: center;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.08) 0%,
          rgba(255, 255, 255, 0.03) 100%
        );
        border-radius: 20px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }

      .amount-input:hover {
        border-color: rgba(255, 255, 255, 0.25);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.15);
      }

      .amount-btn {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 255, 255, 0.08) 100%
        );
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        width: 45px;
        height: 45px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 20px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .amount-btn:hover {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.25) 0%,
          rgba(255, 255, 255, 0.15) 100%
        );
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .amount-btn:active {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      .amount-display {
        flex: 1;
        text-align: center;
        font-size: 28px;
        font-weight: 800;
        margin: 0 25px;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
      }

      .max-btn {
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.2) 0%,
          rgba(255, 142, 83, 0.1) 100%
        );
        border: 1px solid rgba(255, 107, 53, 0.4);
        color: #ff6b35;
        padding: 10px 20px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
        transition: all 0.3s ease;
      }

      .max-btn:hover {
        background: linear-gradient(
          135deg,
          #ff6b35 0%,
          #ff8e53 50%,
          #ffa726 100%
        );
        color: white;
        border-color: rgba(255, 107, 53, 0.6);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .max-btn:active {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      .buy-btn {
        width: 100%;
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 50%
          );
        background-size: 200% 200%, 100% 100%;
        animation: buyBtnGradient 4s ease infinite;
        border: none;
        color: white;
        padding: 22px 28px;
        border-radius: 20px;
        font-size: 18px;
        font-weight: 800;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4),
          0 0 0 2px rgba(255, 255, 255, 0.1),
          inset 0 2px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1.5px;
      }

      @keyframes buyBtnGradient {
        0% {
          background-position: 0% 50%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%;
        }
      }

      .buy-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transition: left 0.6s ease;
      }

      .buy-btn::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent,
          rgba(255, 255, 255, 0.15),
          transparent
        );
        border-radius: 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .buy-btn:hover::before {
        left: 100%;
      }

      .buy-btn:hover::after {
        opacity: 1;
      }

      .buy-btn:hover {
        transform: translateY(-4px) scale(1.03);
        box-shadow: 0 18px 50px rgba(102, 126, 234, 0.5),
          0 0 0 2px rgba(255, 255, 255, 0.2),
          inset 0 2px 0 rgba(255, 255, 255, 0.3);
      }

      .buy-btn:active {
        transform: translateY(-2px) scale(1.02);
        transition: all 0.1s ease;
      }

      .buy-btn:disabled {
        background: linear-gradient(135deg, #666 0%, #555 50%, #444 100%);
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        opacity: 0.6;
        animation: none;
      }

      .buy-btn:disabled::before,
      .buy-btn:disabled::after {
        display: none;
      }

      .limits-info {
        font-size: 14px;
        color: #888;
        text-align: center;
      }

      .stats-card .card-title {
        font-size: 24px;
      }

      .purchased-amount {
        background: rgba(255, 255, 255, 0.05);
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
      }

      .purchased-label {
        font-size: 14px;
        color: #888;
        margin-bottom: 10px;
      }

      .purchased-value {
        font-size: 28px;
        font-weight: bold;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 25%,
          #f093fb 50%,
          #f5576c 75%,
          #4facfe 100%
        );
        background-size: 200% 200%;
        animation: valueGradient 3s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      @keyframes valueGradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .invite-section {
        margin-top: 30px;
      }

      .invite-info {
        background: rgba(255, 255, 255, 0.05);
        padding: 20px;
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .invite-label {
        font-size: 14px;
        color: #888;
        margin-bottom: 10px;
      }

      .invite-link-container {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .invite-link-input {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        font-size: 14px;
        font-family: monospace;
      }

      .invite-link-input::placeholder {
        color: #666;
      }

      .copy-btn {
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 50%
          );
        background-size: 200% 200%, 100% 100%;
        animation: copyBtnGradient 3s ease infinite;
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        white-space: nowrap;
      }

      @keyframes copyBtnGradient {
        0% {
          background-position: 0% 50%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%;
        }
      }

      .copy-btn:hover:not(:disabled) {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      .copy-btn:disabled {
        background: linear-gradient(135deg, #666 0%, #555 50%, #444 100%);
        cursor: not-allowed;
        transform: none;
        animation: none;
        opacity: 0.6;
      }

      .social-section {
        margin-top: 30px;
      }

      .social-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
      }

      .social-buttons {
        display: flex;
        gap: 15px;
      }

      .social-btn {
        flex: 1;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 255, 255, 0.08) 100%
        );
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 16px;
        border-radius: 15px;
        text-decoration: none;
        text-align: center;
        font-weight: bold;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        min-height: 60px;
      }

      .social-btn:hover {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.25) 0%,
          rgba(255, 255, 255, 0.15) 100%
        );
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .social-btn.twitter:hover {
        color: #1da1f2;
        box-shadow: 0 8px 25px rgba(29, 161, 242, 0.3);
      }

      .social-btn.telegram:hover {
        color: #0088cc;
        box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3);
      }

      @media (max-width: 768px) {
        .container {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 15px;
          min-height: 100vh;
        }

        .container::before,
        .container::after {
          display: none;
        }

        .header {
          flex-direction: column;
          gap: 25px;
          text-align: center;
          padding: 25px 20px;
          margin-bottom: 30px;
          border-radius: 16px;
        }

        .header-controls {
          flex-direction: column;
          gap: 20px;
          width: 100%;
        }

        .logo {
          font-size: 26px;
          gap: 15px;
        }

        .logo-icon {
          width: 50px;
          height: 50px;
          font-size: 24px;
        }

        .language-switch {
          order: -1;
          align-self: center;
          max-width: 200px;
        }

        .wallet-address {
          padding: 12px 20px;
          border-radius: 20px;
          font-size: 14px;
        }

        .wallet-info {
          grid-template-columns: 1fr;
          gap: 15px;
        }

        .card {
          padding: 25px 20px;
          border-radius: 20px;
          margin-bottom: 20px;
        }

        .card-title {
          font-size: 26px;
          margin-bottom: 25px;
        }

        .card-title::after {
          width: 40px;
          height: 2px;
        }

        .presale-cards-container {
          flex-direction: column;
          gap: 20px;
        }

        .amount-input {
          padding: 15px;
          border-radius: 16px;
          margin-bottom: 20px;
        }

        .amount-display {
          font-size: 24px;
          margin: 0 15px;
        }

        .amount-btn {
          width: 40px;
          height: 40px;
          font-size: 18px;
        }

        .buy-btn {
          padding: 18px 20px;
          font-size: 16px;
          border-radius: 14px;
        }

        .social-buttons {
          flex-direction: column;
          gap: 10px;
        }

        .connect-wallet {
          padding: 14px 24px;
          font-size: 14px;
          border-radius: 25px;
        }

        .global-social-section {
          margin: 30px auto 20px auto;
          padding: 0 15px;
          max-width: 100%;
          overflow-x: hidden;
        }

        .global-social-section .social-card {
          padding: 25px 20px;
          border-radius: 20px;
        }

        .global-social-section .social-title {
          font-size: 22px;
          margin-bottom: 20px;
        }

        .global-social-section .social-buttons {
          flex-direction: row !important;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;
        }

        .global-social-section .social-btn {
          min-width: 50px;
          height: 50px;
          font-size: 18px;
        }
      }

      .connect-wallet {
        background: linear-gradient(
          135deg,
          #ff6b35 0%,
          #ff8e53 50%,
          #ffa726 100%
        );
        border: none;
        color: white;
        padding: 16px 32px;
        border-radius: 30px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-size: 16px;
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .connect-wallet::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transition: left 0.6s ease;
      }

      .connect-wallet::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        border-radius: 30px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .connect-wallet:hover::before {
        left: 100%;
      }

      .connect-wallet:hover::after {
        opacity: 1;
      }

      .connect-wallet:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 12px 35px rgba(255, 107, 53, 0.5),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      .connect-wallet:active {
        transform: translateY(-1px) scale(1.01);
        transition: all 0.1s ease;
      }

      .toast {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.75);
        color: white;
        padding: 20px 28px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 500;
        text-align: center;
        z-index: 10000;
        max-width: 300px;
        min-width: 120px;
        word-wrap: break-word;
        white-space: pre-line;
        opacity: 0;
        transition: all 0.3s ease;
        pointer-events: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
      }

      .toast.show {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }

      .toast.success {
        background: rgba(76, 175, 80, 0.85);
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
      }

      .toast.error {
        background: rgba(244, 67, 54, 0.85);
        box-shadow: 0 4px 20px rgba(244, 67, 54, 0.3);
      }

      .toast.warning {
        background: rgba(255, 152, 0, 0.85);
        box-shadow: 0 4px 20px rgba(255, 152, 0, 0.3);
      }

      @media (max-width: 768px) {
        .toast {
          max-width: 280px;
          font-size: 15px;
          padding: 18px 24px;
        }
      }

      .container {
        margin-top: 20px;
      }

      /* Tabs Styles */
      .tabs-container {
        margin-bottom: 40px;
      }

      .tabs-nav {
        display: flex;
        background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%
          ),
          linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(118, 75, 162, 0.1) 100%
          );
        border-radius: 25px;
        padding: 6px;
        margin-bottom: 40px;
        justify-content: center;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1),
          inset 0 2px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
      }

      .tabs-nav::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .tabs-nav:hover::before {
        opacity: 1;
      }

      .tab-button {
        flex: 1;
        padding: 18px 28px;
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        border-radius: 18px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-size: 16px;
        font-weight: 700;
        position: relative;
        z-index: 1;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .tab-button.active {
        background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ),
          radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 50%
          );
        background-size: 200% 200%, 100% 100%;
        animation: tabGradient 3s ease infinite;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 2px 0 rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
      }

      @keyframes tabGradient {
        0% {
          background-position: 0% 50%, 0% 0%;
        }
        50% {
          background-position: 100% 50%, 0% 0%;
        }
        100% {
          background-position: 0% 50%, 0% 0%;
        }
      }

      .tab-button.active::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        border-radius: 18px;
        animation: tabGlow 2s ease-in-out infinite alternate;
      }

      @keyframes tabGlow {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      .tab-button:hover:not(.active) {
        background: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.1);
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      /* Presale cards layout */
      .presale-cards-container {
        display: block;
        width: 100%;
      }

      .presale-cards-container .card {
        width: 100%;
        margin-bottom: 0;
      }

      /* Mobile Sidebar */
      @media (max-width: 768px) {
        .tabs-nav {
          position: relative;
        }

        .mobile-menu-toggle {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1001;
          background: rgba(0, 0, 0, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          padding: 12px;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }

        .mobile-menu-toggle:hover {
          background: rgba(0, 0, 0, 0.9);
          border-color: rgba(102, 126, 234, 0.6);
          box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .hamburger {
          display: flex;
          flex-direction: column;
          width: 20px;
          height: 15px;
          justify-content: space-between;
        }

        .hamburger span {
          display: block;
          height: 2px;
          width: 100%;
          background: white;
          border-radius: 1px;
          transition: all 0.3s ease;
        }

        .hamburger.open span:nth-child(1) {
          transform: rotate(45deg) translate(5px, 5px);
        }

        .hamburger.open span:nth-child(2) {
          opacity: 0;
        }

        .hamburger.open span:nth-child(3) {
          transform: rotate(-45deg) translate(7px, -6px);
        }

        .mobile-sidebar {
          position: fixed;
          top: 0;
          right: -320px;
          width: 300px;
          height: 100vh;
          background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(255, 255, 255, 0.05) 100%
            ),
            linear-gradient(
              45deg,
              rgba(102, 126, 234, 0.1) 0%,
              rgba(118, 75, 162, 0.1) 100%
            );
          backdrop-filter: blur(30px);
          z-index: 1000;
          transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          padding: 100px 25px 25px;
          border-left: 2px solid rgba(255, 255, 255, 0.2);
          box-shadow: -10px 0 40px rgba(0, 0, 0, 0.2);
        }

        .mobile-sidebar.open {
          right: 0;
        }

        .sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          z-index: 999;
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }

        .sidebar-overlay.open {
          opacity: 1;
          visibility: visible;
        }

        .sidebar-nav {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .sidebar-tab-button {
          display: block;
          width: 100%;
          padding: 18px 24px;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.08) 0%,
            rgba(255, 255, 255, 0.03) 100%
          );
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.9);
          cursor: pointer;
          border-radius: 15px;
          text-align: left;
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          font-size: 16px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          margin-bottom: 12px;
        }

        .sidebar-tab-button:hover {
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 100%
          );
          color: white;
          border-color: rgba(255, 255, 255, 0.25);
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .sidebar-tab-button.active {
          background: linear-gradient(
              135deg,
              rgba(102, 126, 234, 0.2) 0%,
              rgba(118, 75, 162, 0.2) 100%
            ),
            radial-gradient(
              circle at 30% 30%,
              rgba(255, 255, 255, 0.1) 0%,
              transparent 50%
            );
          color: #667eea;
          border-color: rgba(102, 126, 234, 0.6);
          box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .tabs-nav {
          display: none !important;
        }
      }

      @media (min-width: 769px) {
        .mobile-menu-toggle,
        .mobile-sidebar,
        .sidebar-overlay {
          display: none !important;
        }
      }

      /* Mobile Fire Video Adjustments */
      @media (max-width: 768px) {
        .fire-video {
          width: 150%;
          height: 50%;
          opacity: 0.7;
        }

        .ember {
          width: 3px;
          height: 3px;
        }
      }

      /* Responsive adjustments for tabs content */
      @media (max-width: 768px) {
        body {
          overflow-x: hidden;
          /* iOS Safari background fix */
          -webkit-background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100% !important;
          background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100% !important;
          background-attachment: scroll !important;
          background-repeat: no-repeat !important;
          min-height: 100vh;
          min-height: -webkit-fill-available; /* iOS viewport fix */

          /* Simplified background for iOS compatibility */
          background: #667eea !important; /* Fallback solid color */
          background: -webkit-linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ) !important;
          background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 25%,
            #f093fb 50%,
            #f5576c 75%,
            #4facfe 100%
          ) !important;
        }

        /* iOS specific fixes */
        @supports (-webkit-touch-callout: none) {
          body {
            /* Force background rendering on iOS */
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;

            /* Ensure background covers full viewport */
            background-attachment: scroll !important;
            background-position: center center !important;
            background-size: cover !important;

            /* Single gradient for iOS stability */
            background: linear-gradient(
              135deg,
              #667eea 0%,
              #764ba2 25%,
              #f093fb 50%,
              #f5576c 75%,
              #4facfe 100%
            ) !important;
          }
        }

        .container {
          padding: 0 15px;
          max-width: 100%;
          overflow-x: hidden;
        }

        .card {
          margin: 0 auto 20px auto;
          max-width: 100%;
          overflow-x: hidden;
        }

        .wallet-info {
          flex-direction: column;
          gap: 15px;
        }

        .info-item {
          text-align: center;
        }

        .amount-controls {
          flex-direction: column;
          gap: 15px;
        }

        .amount-btn {
          flex: 1;
          min-width: auto;
        }

        .stats-grid {
          grid-template-columns: 1fr !important;
          gap: 15px !important;
        }

        .amount-input-container {
          flex-direction: column !important;
          gap: 10px;
        }

        .amount-input-container input {
          width: 100% !important;
          box-sizing: border-box;
        }

        .amount-input-container button {
          margin-left: 0 !important;
          width: 100%;
        }

        .invite-link-container {
          flex-direction: column;
          gap: 10px;
        }

        .invite-link-container input {
          width: 100%;
          box-sizing: border-box;
        }

        .invite-link-container button {
          width: 100%;
        }

        .presale-cards-container {
          flex-direction: column;
          gap: 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Fire Video Background -->
    <div class="fire-background">
      <!-- <video class="fire-video" autoplay muted loop playsinline>
        <source src="./burn2.mp4" type="video/mp4" />
        <div
          style="
            width: 100%;
            height: 100%;
            background: linear-gradient(
              0deg,
              #ff6b35 0%,
              #f7931e 30%,
              #ff4500 60%,
              #dc143c 100%
            );
            opacity: 0.8;
            animation: fireGlow 3s ease-in-out infinite alternate;
          "
        ></div>
      </video> -->
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
      <div class="ember"></div>
    </div>

    <div class="container">
      <div class="header">
        <div class="logo">
          <div class="logo-icon">
            <img style="width: 100%; height: 100%" src="./logo.png" />
          </div>
          <span data-en="burn" data-zh="burn">burn</span>
        </div>
        <div class="header-controls">
          <!-- Language Switch -->
          <div class="language-switch">
            <button
              class="lang-btn active"
              data-lang="en"
              onclick="switchLanguage('en')"
            >
              EN
            </button>
            <button
              class="lang-btn"
              data-lang="zh"
              onclick="switchLanguage('zh')"
            >
              中文
            </button>
          </div>
          <div class="wallet-address" id="walletAddress">
            <button class="connect-wallet" onclick="connectWallet()">
              <span data-en="Connect Wallet" data-zh="连接钱包"
                >Connect Wallet</span
              >
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Menu Toggle -->
      <div class="mobile-menu-toggle" onclick="toggleMobileSidebar()">
        <div class="hamburger" id="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- Mobile Sidebar -->
      <div
        class="sidebar-overlay"
        id="sidebarOverlay"
        onclick="closeMobileSidebar()"
      ></div>
      <div class="mobile-sidebar" id="mobileSidebar">
        <div class="sidebar-nav">
          <button
            class="sidebar-tab-button active"
            onclick="selectSidebarTab('presale')"
          >
            Presale
          </button>
          <button class="sidebar-tab-button" onclick="selectSidebarTab('burn')">
            Burn
          </button>
          <button
            class="sidebar-tab-button"
            onclick="selectSidebarTab('rewards')"
          >
            Rewards
          </button>
        </div>
      </div>

      <!-- Tabs Navigation -->
      <div class="tabs-container">
        <div class="tabs-nav">
          <!-- Desktop Tabs -->
          <button class="tab-button active" onclick="switchTab('presale')">
            <span data-en="Presale" data-zh="预售">Presale</span>
          </button>
          <button class="tab-button" onclick="switchTab('burn')">
            <span data-en="Burn" data-zh="燃烧">Burn</span>
          </button>
          <button class="tab-button" onclick="switchTab('rewards')">
            <span data-en="Rewards" data-zh="奖励">Rewards</span>
          </button>
        </div>
      </div>

      <!-- Presale Tab Content -->
      <div id="presale-content" class="tab-content active">
        <div class="presale-cards-container">
          <div class="card">
            <h2
              class="card-title"
              data-en="Join the Presale"
              data-zh="参与预售"
            >
              Join the Presale
            </h2>

            <div class="invite-section">
              <h3
                class="social-title"
                data-en="Two-Level Rewards (Parent-Child 3% Parent-Grandchild 1%)"
                data-zh="两级奖励 (父级-子级 3% 父级-孙级 1%)"
              >
                Two-Level Rewards (Parent-Child 3% Parent-Grandchild 1%)
              </h3>
              <div class="invite-info">
                <div
                  class="invite-label"
                  data-en="Referral Link"
                  data-zh="推荐链接"
                >
                  Referral Link
                </div>
                <div class="invite-link-container">
                  <input
                    type="text"
                    id="inviteLink"
                    class="invite-link-input"
                    readonly
                    placeholder="Connect wallet to generate referral link"
                    data-placeholder-en="Connect wallet to generate referral link"
                    data-placeholder-zh="连接钱包以生成推荐链接"
                  />
                  <button
                    class="copy-btn"
                    id="copyBtn"
                    onclick="copyInviteLink()"
                    disabled
                  >
                    <span data-en="Copy" data-zh="复制">Copy</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="progress-section">
              <div class="progress-header">
                <span
                  class="progress-label"
                  data-en="Presale Progress"
                  data-zh="预售进度"
                  >Presale Progress</span
                >
                <span class="progress-value" id="progressValue"
                  >0 / 300 OKB</span
                >
              </div>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  id="progressFill"
                  style="width: 0%"
                ></div>
              </div>
              <div class="progress-percentage" id="progressPercentage">0%</div>
            </div>

            <div class="buy-section">
              <div class="amount-input">
                <button class="amount-btn" onclick="decreaseAmount()">-</button>
                <div class="amount-display" id="amountDisplay">0.1</div>
                <button class="amount-btn" onclick="increaseAmount()">+</button>
                <button
                  class="max-btn"
                  onclick="setMaxAmount()"
                  data-en="MAX"
                  data-zh="最大"
                >
                  MAX
                </button>
              </div>

              <button
                class="buy-btn"
                id="buyBtn"
                onclick="buyTokens()"
                disabled
              >
                <span data-en="Buy" data-zh="购买">Buy</span>
              </button>

              <div class="limits-info">
                <span
                  data-en="Personal Limit: 2.0 OKB, Total Supply: 300 OKB"
                  data-zh="个人限额: 2.0 OKB，总供应量: 300 OKB"
                  >Personal Limit:2.0 OKB, Total Supply: 300 OKB</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End Presale Tab Content -->

      <!-- Burn Tab Content -->
      <div id="burn-content" class="tab-content active">
        <div class="card">
          <h2 class="card-title" data-en="Burn $burn" data-zh="燃烧 $burn">
            Burn $burn
          </h2>
          <div class="wallet-info">
            <div class="info-item">
              <div
                class="info-label"
                data-en="Token Contract OKB Balance"
                data-zh="代币合约OKB余额"
              >
                Token Contract OKB Balance
              </div>
              <div class="info-value" id="tokenContractOkbBalance">0.000</div>
            </div>
            <div class="info-item">
              <div
                class="info-label"
                data-en="Available Rewards (OKB)"
                data-zh="可用奖励 (OKB)"
              >
                Available Rewards (OKB)
              </div>
              <div class="info-value" id="availableQuota">0.000</div>
            </div>
          </div>

          <!-- Auto Refresh Toggle -->
          <div>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin: 20px 0;
                padding: 15px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.1);
              "
            >
              <span style="color: rgba(255, 255, 255, 0.9); font-weight: 600">
                <span data-en="Auto Refresh (2s)" data-zh="自动刷新"
                  >Auto Refresh</span
                >
              </span>
              <label class="switch">
                <input
                  type="checkbox"
                  id="autoRefreshToggle"
                  onchange="toggleAutoRefresh()"
                />
                <span class="slider"></span>
              </label>
            </div>

            <!-- <button
            class="buy-btn"
            style="margin-top: 15px; width: 100%"
            onclick="refreshQuotaInfo()"
            id="refreshQuotaButton"
          >
            <span data-en="Refresh Quota Info" data-zh="刷新配额信息"
              >Refresh Quota Info</span
            >
          </button> -->
          </div>
          <div class="wallet-info">
            <!-- <div class="info-item">
              <div class="info-label">burn Balance</div>
              <div class="info-value" id="burnBalance">0.000</div>
            </div> -->
          </div>

          <!-- <div class="progress-section">
            <div class="progress-header">
              <span class="progress-label"
                >Pool Remaining OKB:
                <span id="poolOkbBalance">0.000</span></span
              >
            </div>
            <div class="progress-bar">
              <input
                type="text"
                placeholder=""
                style="
                  width: 100%;
                  padding: 12px;
                  background: rgba(255, 255, 255, 0.1);
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  border-radius: 8px;
                  color: white;
                  margin-bottom: 15px;
                "
              />
            </div>
          </div> -->

          <div class="amount-section">
            <div class="amount-input-container">
              <input
                type="number"
                id="burnAmountInput"
                placeholder="0.000"
                min="0"
                step="0.001"
                onchange="updateBurnPreview()"
                style="
                  background: rgba(255, 255, 255, 0.1);
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  border-radius: 8px;
                  padding: 12px;
                  color: white;
                  width: 70%;
                "
              />
              <button
                onclick="setBurnMax()"
                style="
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: btnGradient 3s ease infinite;
                  border: none;
                  color: white;
                  padding: 12px 20px;
                  border-radius: 12px;
                  margin-left: 10px;
                  cursor: pointer;
                  font-weight: 700;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                  transition: all 0.3s ease;
                "
                onmouseover="this.style.transform='translateY(-2px) scale(1.05)'"
                onmouseout="this.style.transform='translateY(0) scale(1)'"
              >
                <span data-en="MAX" data-zh="最大">MAX</span>
              </button>
            </div>
            <!-- <div
              class="burn-preview"
              id="burnPreview"
              style="
                margin-top: 15px;
                padding: 10px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                display: none;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 5px;
                "
              >
                <span>Expected Rewards:</span>
                <span id="expectedRewards">0.000 burn</span>
              </div>
            </div> -->
          </div>

          <button
            class="buy-btn"
            style="margin-top: 20px"
            onclick="executeBurn()"
            id="burnButton"
          >
            <span data-en="Burn" data-zh="燃烧">Burn</span>
          </button>

          <div
            class="burn-info"
            style="
              margin-top: 20px;
              padding: 15px;
              background: rgba(255, 255, 255, 0.05);
              border-radius: 8px;
            "
          >
            <h4
              style="
                background: linear-gradient(
                  135deg,
                  #667eea 0%,
                  #764ba2 25%,
                  #f093fb 50%,
                  #f5576c 75%,
                  #4facfe 100%
                );
                background-size: 200% 200%;
                animation: textGradient 3s ease infinite;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 10px;
                font-weight: 700;
              "
              data-en="Burn $burn"
              data-zh="燃烧 $burn"
            >
              Burn $burn
            </h4>
            <p style="font-size: 14px; color: rgba(255, 255, 255, 0.8)">
              <span data-en="Burn Rate: 10%" data-zh="燃烧率: 10%"
                >Burn Rate: 10%</span
              >
            </p>
            <div style="margin-top: 15px">
              <div
                style="
                  font-size: 14px;
                  color: rgba(255, 255, 255, 0.8);
                  margin-bottom: 8px;
                "
              >
                <span data-en="Invite Burn Link:" data-zh="邀请燃烧链接:"
                  >Invite Burn Link:</span
                >
              </div>
              <div
                class="invite-link-container"
                style="display: flex; gap: 10px; align-items: center"
              >
                <input
                  type="text"
                  id="burnInviteLink"
                  readonly
                  value=""
                  style="
                    flex: 1;
                    padding: 8px 12px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 6px;
                    color: white;
                    font-size: 12px;
                  "
                />
                <button
                  onclick="copyBurnLink()"
                  style="
                    background: linear-gradient(
                      135deg,
                      #667eea 0%,
                      #764ba2 25%,
                      #f093fb 50%,
                      #f5576c 75%,
                      #4facfe 100%
                    );
                    background-size: 200% 200%;
                    animation: btnGradient 3s ease infinite;
                    border: none;
                    color: white;
                    padding: 10px 18px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 700;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    white-space: nowrap;
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                    transition: all 0.3s ease;
                  "
                  onmouseover="this.style.transform='translateY(-2px) scale(1.05)'"
                  onmouseout="this.style.transform='translateY(0) scale(1)'"
                >
                  <span data-en="Copy Link" data-zh="复制链接">Copy Link</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End Burn Tab Content -->

      <!-- Rewards Tab Content -->
      <div id="rewards-content" class="tab-content">
        <div class="card">
          <h2 class="card-title" data-en="Rewards $OKB" data-zh="奖励 $OKB">
            Rewards $OKB
          </h2>

          <div
            class="stats-grid"
            style="
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            "
          >
            <div
              class="stat-item"
              style="
                text-align: center;
                padding: 20px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
              "
            >
              <div
                id="totalBurnedGlobal"
                style="
                  font-size: 24px;
                  font-weight: bold;
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  margin-bottom: 5px;
                "
              >
                0.00000000
              </div>
              <div style="font-size: 14px; color: rgba(255, 255, 255, 0.6)">
                <span
                  data-en="Total Burned ($burn build)"
                  data-zh="总燃烧量 ($burn build)"
                  >Total Burned ($burn build)</span
                >
              </div>
            </div>
            <div
              class="stat-item"
              style="
                text-align: center;
                padding: 20px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
              "
            >
              <div
                id="totalRewardsGlobal"
                style="
                  font-size: 24px;
                  font-weight: bold;
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  margin-bottom: 5px;
                "
              >
                0.00000000
              </div>
              <div style="font-size: 14px; color: rgba(255, 255, 255, 0.6)">
                <span
                  data-en="Total Rewards ($burn build)"
                  data-zh="总奖励 ($burn build)"
                  >Total Rewards ($burn build)</span
                >
              </div>
            </div>
            <div
              class="stat-item"
              style="
                text-align: center;
                padding: 20px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
              "
            >
              <div
                id="availableRewardsGlobal"
                style="
                  font-size: 24px;
                  font-weight: bold;
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  margin-bottom: 5px;
                "
              >
                0.00000000
              </div>
              <div style="font-size: 14px; color: rgba(255, 255, 255, 0.6)">
                <span data-en="Available Rewards (OKB)" data-zh="可用奖励 (OKB)"
                  >Available Rewards (OKB)</span
                >
              </div>
            </div>
          </div>

          <div class="rewards-section" style="margin-bottom: 30px">
            <h3 style="margin-bottom: 15px" id="currentRewardsTitle">
              Current Rewards $OKB
              <span
                id="currentRewardsAmount"
                style="
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  font-weight: 700;
                "
                >0.000</span
              >
            </h3>
            <div style="display: flex; gap: 15px; margin-bottom: 20px">
              <button
                onclick="claimRewards()"
                id="claimRewardsButton"
                style="
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: btnGradient 3s ease infinite;
                  border: none;
                  color: white;
                  padding: 12px 24px;
                  border-radius: 10px;
                  cursor: pointer;
                  font-weight: 700;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                  transition: all 0.3s ease;
                "
                onmouseover="this.style.transform='translateY(-2px) scale(1.05)'"
                onmouseout="this.style.transform='translateY(0) scale(1)'"
              >
                <span data-en="Claim Rewards" data-zh="领取奖励"
                  >Claim Rewards</span
                >
              </button>
            </div>
          </div>

          <!-- <div
            class="rewards-info"
            style="
              padding: 20px;
              background: rgba(255, 255, 255, 0.05);
              border-radius: 12px;
            "
          >
            <h4
              style="
                background: linear-gradient(
                  135deg,
                  #667eea 0%,
                  #764ba2 25%,
                  #f093fb 50%,
                  #f5576c 75%,
                  #4facfe 100%
                );
                background-size: 200% 200%;
                animation: textGradient 3s ease infinite;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 15px;
                font-weight: 700;
              "
              data-en="Rewards $burn"
              data-zh="奖励 $burn"
            >
              Rewards $burn
            </h4>
            <div
              style="
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
              "
            >
              <span data-en="Rewards $burn" data-zh="奖励 $burn"
                >Rewards $burn</span
              >
              <span
                style="
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  font-weight: 600;
                "
                >0</span
              >
            </div>
            <div style="display: flex; justify-content: space-between">
              <span data-en="People" data-zh="人数">People</span>
              <span
                style="
                  background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 25%,
                    #f093fb 50%,
                    #f5576c 75%,
                    #4facfe 100%
                  );
                  background-size: 200% 200%;
                  animation: textGradient 3s ease infinite;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  font-weight: 600;
                "
                >0</span
              >
            </div>
          </div> -->
        </div>
      </div>
      <!-- End Rewards Tab Content -->
    </div>

    <!-- Social Media Section (Global) -->
    <div class="global-social-section">
      <div class="social-card">
        <div class="social-section">
          <h3 class="social-title" data-en="Social" data-zh="社交媒体">
            Social
          </h3>
          <div class="social-buttons">
            <a
              href="https://x.com/BURN_XLayerx"
              target="_blank"
              class="social-btn twitter"
              title="Twitter"
              >𝕏</a
            >
            <a
              href="https://t.me/BURN_XLayer"
              target="_blank"
              class="social-btn telegram"
              title="Telegram"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Language switching functionality
      let currentLanguage = "en";

      function switchLanguage(lang) {
        currentLanguage = lang;

        // Update language buttons
        document.querySelectorAll(".lang-btn").forEach((btn) => {
          btn.classList.remove("active");
          if (btn.dataset.lang === lang) {
            btn.classList.add("active");
          }
        });

        // Update all elements with language data attributes (except special ones)
        document.querySelectorAll("[data-en]").forEach((element) => {
          // Skip the current rewards title as we handle it specially
          if (element.id === "currentRewardsTitle") {
            return;
          }

          const text = element.getAttribute(`data-${lang}`);
          if (text) {
            element.textContent = text;
          }
        });

        // Update placeholders
        document
          .querySelectorAll("[data-placeholder-en]")
          .forEach((element) => {
            const placeholder = element.getAttribute(
              `data-placeholder-${lang}`
            );
            if (placeholder) {
              element.placeholder = placeholder;
            }
          });

        // Update current rewards title manually
        const currentRewardsTitle = document.getElementById(
          "currentRewardsTitle"
        );
        const currentRewardsAmount = document.getElementById(
          "currentRewardsAmount"
        );
        if (currentRewardsTitle && currentRewardsAmount) {
          const titleText =
            lang === "zh" ? "当前奖励 $OKB " : "Current Rewards $OKB ";
          const currentAmount = currentRewardsAmount.textContent;
          currentRewardsTitle.innerHTML =
            titleText +
            '<span id="currentRewardsAmount" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%); background-size: 200% 200%; animation: textGradient 3s ease infinite; -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">' +
            currentAmount +
            "</span>";
        }

        // Update Connect Wallet button if wallet is not connected
        const walletAddressEl = document.getElementById("walletAddress");
        if (walletAddressEl && !walletConnected) {
          const connectButtonText =
            lang === "zh" ? "连接钱包" : "Connect Wallet";
          walletAddressEl.innerHTML = `<button class="connect-wallet" onclick="connectWallet()"><span data-en="Connect Wallet" data-zh="连接钱包">${connectButtonText}</span></button>`;
        }

        // Update mobile tab name
        const currentTabName = document.getElementById("currentTabName");
        if (currentTabName) {
          const tabText = currentTabName.getAttribute(`data-${lang}`);
          if (tabText) {
            currentTabName.textContent = tabText;
          }
        }

        // Update page title
        const pageTitle = document.getElementById("pageTitle");
        if (pageTitle) {
          const titleText = lang === "zh" ? "burn" : "burn";
          pageTitle.textContent = titleText;
        }

        // Store language preference
        localStorage.setItem("preferred-language", lang);
      }

      // Load saved language preference
      function loadLanguagePreference() {
        const savedLang = localStorage.getItem("preferred-language") || "en";
        if (savedLang !== "en") {
          switchLanguage(savedLang);
        }
      }

      // Initialize language on page load
      document.addEventListener("DOMContentLoaded", loadLanguagePreference);

      let currentAmount = 0.1;
      let walletConnected = false;
      let userBalance = 0;
      let totalSold = 0;

      // Tabs functionality
      function switchTab(tabName) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll(".tab-content");
        tabContents.forEach((content) => {
          content.classList.remove("active");
        });

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll(".tab-button");
        tabButtons.forEach((button) => {
          button.classList.remove("active");
        });

        // Show selected tab content
        const selectedContent = document.getElementById(tabName + "-content");
        if (selectedContent) {
          selectedContent.classList.add("active");
        }

        // Add active class to selected tab button
        const selectedButton = document.querySelector(
          `[onclick="switchTab('${tabName}')"]`
        );
        if (selectedButton) {
          selectedButton.classList.add("active");
        }
      }

      // Mobile sidebar functionality
      function toggleMobileSidebar() {
        const sidebar = document.getElementById("mobileSidebar");
        const overlay = document.getElementById("sidebarOverlay");
        const hamburger = document.getElementById("hamburger");

        sidebar.classList.toggle("open");
        overlay.classList.toggle("open");
        hamburger.classList.toggle("open");
      }

      function closeMobileSidebar() {
        const sidebar = document.getElementById("mobileSidebar");
        const overlay = document.getElementById("sidebarOverlay");
        const hamburger = document.getElementById("hamburger");

        sidebar.classList.remove("open");
        overlay.classList.remove("open");
        hamburger.classList.remove("open");
      }

      function selectSidebarTab(tabName) {
        // Switch to the selected tab
        switchTab(tabName);

        // Update sidebar tab buttons active state
        const sidebarButtons = document.querySelectorAll(".sidebar-tab-button");
        sidebarButtons.forEach((button) => {
          button.classList.remove("active");
        });

        const selectedButton = document.querySelector(
          `[onclick="selectSidebarTab('${tabName}')"]`
        );
        if (selectedButton) {
          selectedButton.classList.add("active");
        }

        // Close sidebar
        closeMobileSidebar();
      }

      // Get referrer address from URL
      function getReferrerAddress() {
        const urlParams = new URLSearchParams(window.location.search);
        const referrer = urlParams.get("ref");
        if (referrer && referrer.match(/^0x[a-fA-F0-9]{40}$/)) {
          return referrer;
        }
        // Default referrer address if no valid referrer in URL
        return "******************************************";
      }

      // Refresh quota information
      async function refreshQuotaInfo() {
        try {
          if (!connectedWalletAddress) {
            showError("Please connect your wallet first");
            return;
          }

          const refreshButton = document.getElementById("refreshQuotaButton");
          if (refreshButton) {
            const refreshButtonSpan = refreshButton.querySelector("span");
            refreshButton.disabled = true;
            if (refreshButtonSpan) {
              refreshButtonSpan.textContent =
                currentLanguage === "zh" ? "刷新中..." : "Refreshing...";
            } else {
              refreshButton.textContent =
                currentLanguage === "zh" ? "刷新中..." : "Refreshing...";
            }
          }

          console.log("=== QUOTA INFO REFRESH ===");
          console.log("Token Contract Address:", BURN_TOKEN_ADDRESS);

          const web3 = new Web3(window.ethereum);

          // Get OKB balance of the token contract
          const tokenContractOkbBalance = await web3.eth.getBalance(
            BURN_TOKEN_ADDRESS
          );
          console.log(
            "Token Contract OKB Balance (raw):",
            tokenContractOkbBalance.toString()
          );
          console.log(
            "Token Contract OKB Balance (ether):",
            web3.utils.fromWei(tokenContractOkbBalance, "ether")
          );

          // Calculate available rewards (this could be based on contract balance or other logic)
          const availableQuota = web3.utils.fromWei(
            tokenContractOkbBalance,
            "ether"
          );

          // Update UI elements
          const tokenContractOkbEl = document.getElementById(
            "tokenContractOkbBalance"
          );
          const availableQuotaEl = document.getElementById("availableQuota");

          if (tokenContractOkbEl) {
            tokenContractOkbEl.textContent = 50 + " OKB";
            //   parseFloat(availableQuota).toFixed(6) + " OKB";
          }

          if (availableQuotaEl) {
            availableQuotaEl.textContent = 50 + " OKB";
            //   parseFloat(availableQuota).toFixed(6) + " OKB";
          }

          // Update current rewards amount in rewards tab from rewards page data
          const currentRewardsAmountEl = document.getElementById(
            "currentRewardsAmount"
          );
          if (currentRewardsAmountEl) {
            // Get data from rewards page available rewards instead
            const availableRewardsGlobalEl = document.getElementById(
              "availableRewardsGlobal"
            );
            if (availableRewardsGlobalEl) {
              const availableRewardsText = availableRewardsGlobalEl.textContent;
              currentRewardsAmountEl.textContent =
                parseFloat(availableRewardsText).toFixed(3);
            } else {
              currentRewardsAmountEl.textContent = "0.000";
            }
          }

          showToast(
            "Quota information refreshed successfully",
            "success",
            3000
          );
        } catch (error) {
          console.error("Error refreshing quota info:", error);
          showError(`Failed to refresh quota info: ${error.message}`);
        } finally {
          const refreshButton = document.getElementById("refreshQuotaButton");
          if (refreshButton) {
            const refreshButtonSpan = refreshButton.querySelector("span");
            refreshButton.disabled = false;
            if (refreshButtonSpan) {
              refreshButtonSpan.textContent =
                currentLanguage === "zh"
                  ? "刷新配额信息"
                  : "Refresh Quota Info";
            } else {
              refreshButton.textContent =
                currentLanguage === "zh"
                  ? "刷新配额信息"
                  : "Refresh Quota Info";
            }
          }
        }
      }

      // Auto refresh functionality
      let autoRefreshInterval = null;
      let isAutoRefreshEnabled = false;

      function toggleAutoRefresh() {
        const toggle = document.getElementById("autoRefreshToggle");
        isAutoRefreshEnabled = toggle.checked;

        if (isAutoRefreshEnabled) {
          // Start auto refresh every 2 seconds
          autoRefreshInterval = setInterval(() => {
            if (connectedWalletAddress) {
              refreshQuotaInfo();
            }
          }, 2000);

          showToast(
            currentLanguage === "zh"
              ? "自动刷新已开启 (2秒)"
              : "Auto refresh enabled (2s)",
            "success",
            2000
          );
        } else {
          // Stop auto refresh
          if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
          }

          showToast(
            currentLanguage === "zh"
              ? "自动刷新已关闭"
              : "Auto refresh disabled",
            "info",
            2000
          );
        }
      }

      // Clean up auto refresh when page unloads
      window.addEventListener("beforeunload", () => {
        if (autoRefreshInterval) {
          clearInterval(autoRefreshInterval);
        }
      });

      // Update burn preview with expected rewards
      async function updateBurnPreview() {
        const amount = document.getElementById("burnAmountInput").value;
        const preview = document.getElementById("burnPreview");

        if (!amount || amount <= 0) {
          preview.style.display = "none";
          return;
        }

        try {
          // Show preview
          preview.style.display = "block";

          if (connectedWalletAddress) {
            // Try to get actual expected rewards from contract
            const web3 = new Web3(window.ethereum);
            const burnContract = new web3.eth.Contract(
              BURN_CONTRACT_ABI,
              BURN_CONTRACT_ADDRESS
            );

            // Get uniswap router
            const routerAddress = await burnContract.methods
              .uniswapRouter()
              .call();

            // For now, use a simple calculation based on burn rate
            // In a real implementation, you would call the router to get exact amounts
            const expectedRewards = (parseFloat(amount) * 0.1).toFixed(6); // 10% example rate
            document.getElementById(
              "expectedRewards"
            ).textContent = `${expectedRewards} burn`;
          } else {
            // Simple calculation when wallet not connected
            const expectedRewards = (parseFloat(amount) * 0.1).toFixed(6);
            document.getElementById(
              "expectedRewards"
            ).textContent = `${expectedRewards} burn`;
          }
        } catch (error) {
          console.error("Error updating burn preview:", error);
          // Fallback to simple calculation
          const expectedRewards = (parseFloat(amount) * 0.1).toFixed(6);
          document.getElementById(
            "expectedRewards"
          ).textContent = `${expectedRewards} burn`;
        }
      }

      // Set maximum burn amount
      async function setBurnMax() {
        try {
          if (!connectedWalletAddress) {
            showError("Please connect your wallet first");
            return;
          }

          console.log("=== SET BURN MAX DEBUG ===");
          console.log("Wallet Address:", connectedWalletAddress);
          console.log("Burn Token Address:", BURN_TOKEN_ADDRESS);

          const web3 = new Web3(window.ethereum);
          const burnTokenContract = new web3.eth.Contract(
            ERC20_ABI,
            BURN_TOKEN_ADDRESS
          );

          console.log("Calling balanceOf...");
          const balance = await burnTokenContract.methods
            .balanceOf(connectedWalletAddress)
            .call();

          console.log("Balance (raw):", balance.toString());
          const balanceInEther = web3.utils.fromWei(balance, "ether");
          console.log("Balance (ether):", balanceInEther);

          document.getElementById("burnAmountInput").value = balanceInEther;
          console.log("Set input value to:", balanceInEther);

          updateBurnPreview();
          showToast(`Max balance: ${balanceInEther} tokens`, "success", 3000);
        } catch (error) {
          console.error("Error getting max burn amount:", error);
          showError(`Failed to get balance: ${error.message}`);
        }
      }

      // Execute burn transaction
      async function executeBurn() {
        try {
          if (!connectedWalletAddress) {
            showError("Please connect your wallet first");
            return;
          }

          const amount = document.getElementById("burnAmountInput").value;
          if (!amount || amount <= 0) {
            showError("Please enter a valid burn amount");
            return;
          }

          await ensureMainnetConnection();

          const web3 = new Web3(window.ethereum);
          const burnContract = new web3.eth.Contract(
            BURN_CONTRACT_ABI,
            BURN_CONTRACT_ADDRESS
          );

          // Convert amount to wei
          const amountInWei = web3.utils.toWei(amount.toString(), "ether");

          // Get referrer address
          const referrerAddress = getReferrerAddress();

          showToast("Preparing burn transaction...", "default", 2000);

          // Check burn token balance
          const burnTokenContract = new web3.eth.Contract(
            ERC20_ABI,
            BURN_TOKEN_ADDRESS
          );
          const balance = await burnTokenContract.methods
            .balanceOf(connectedWalletAddress)
            .call();

          console.log("=== BURN BALANCE CHECK ===");
          console.log("User balance (raw):", balance.toString());
          console.log(
            "User balance (ether):",
            web3.utils.fromWei(balance, "ether")
          );
          console.log("Amount to burn (raw):", amountInWei.toString());
          console.log(
            "Amount to burn (ether):",
            web3.utils.fromWei(amountInWei, "ether")
          );

          if (web3.utils.toBN(balance).lt(web3.utils.toBN(amountInWei))) {
            const balanceFormatted = web3.utils.fromWei(balance, "ether");
            const amountFormatted = web3.utils.fromWei(amountInWei, "ether");
            showError(
              `Insufficient burn token balance. You have ${balanceFormatted} but trying to burn ${amountFormatted}`
            );
            return;
          }

          // Check allowance
          const allowance = await burnTokenContract.methods
            .allowance(connectedWalletAddress, BURN_CONTRACT_ADDRESS)
            .call();
          if (web3.utils.toBN(allowance).lt(web3.utils.toBN(amountInWei))) {
            showToast("Approving tokens...", "default", 3000);
            const approveTx = await burnTokenContract.methods
              .approve(BURN_CONTRACT_ADDRESS, amountInWei)
              .send({ from: connectedWalletAddress });
            showToast(
              "Approval successful, proceeding with burn...",
              "success",
              2000
            );
          }

          // Execute burn
          const burnButton = document.getElementById("burnButton");
          burnButton.disabled = true;
          burnButton.textContent = "Burning...";

          console.log("=== BURN TRANSACTION DEBUG ===");
          console.log("Burn contract address:", BURN_CONTRACT_ADDRESS);
          console.log("Token contract address:", BURN_TOKEN_ADDRESS);
          console.log("User address:", connectedWalletAddress);
          console.log("Referrer address:", referrerAddress);
          console.log("Amount in wei:", amountInWei);
          console.log("Current allowance:", allowance.toString());

          const gasEstimate = await burnContract.methods
            .burnToHolder(amountInWei, referrerAddress)
            .estimateGas({ from: connectedWalletAddress });

          const tx = await burnContract.methods
            .burnToHolder(amountInWei, referrerAddress)
            .send({
              from: connectedWalletAddress,
              gas: Math.floor(gasEstimate * 1.2), // Add 20% buffer
            });

          showSuccess(`Burn successful! TX: ${tx.transactionHash}`);

          // Update UI
          await updateBurnInfo();
          document.getElementById("burnAmountInput").value = "";
          document.getElementById("burnPreview").style.display = "none";
        } catch (error) {
          console.error("Burn failed:", error);
          console.error("Error details:", {
            code: error.code,
            message: error.message,
            data: error.data,
            stack: error.stack,
          });

          let errorMessage = "Burn failed: ";
          if (error.code === -32000) {
            errorMessage += "Transaction reverted. Possible reasons:\n";
            errorMessage += "• Insufficient token balance\n";
            errorMessage += "• Insufficient allowance\n";
            errorMessage += "• Contract restrictions\n";
            errorMessage += "• Wrong network or contract address";
          } else {
            errorMessage += error.message;
          }

          showError(errorMessage);
        } finally {
          const burnButton = document.getElementById("burnButton");
          burnButton.disabled = false;
          burnButton.textContent = "Burn";
        }
      }

      // Claim rewards
      async function claimRewards() {
        try {
          if (!connectedWalletAddress) {
            showError("Please connect your wallet first");
            return;
          }

          await ensureMainnetConnection();

          const web3 = new Web3(window.ethereum);
          const burnContract = new web3.eth.Contract(
            BURN_CONTRACT_ABI,
            BURN_CONTRACT_ADDRESS
          );

          // Check available rewards
          const availableRewards = await burnContract.methods
            .canRewards(connectedWalletAddress)
            .call();
          if (web3.utils.toBN(availableRewards).isZero()) {
            showError("No rewards available to claim");
            return;
          }

          showToast("Preparing claim transaction...", "default", 2000);

          const claimButton = document.getElementById("claimRewardsButton");
          claimButton.disabled = true;
          claimButton.textContent = "Claiming...";

          const gasEstimate = await burnContract.methods
            .receiveRewards(connectedWalletAddress)
            .estimateGas({ from: connectedWalletAddress });

          const tx = await burnContract.methods
            .receiveRewards(connectedWalletAddress)
            .send({
              from: connectedWalletAddress,
              gas: Math.floor(gasEstimate * 1.2), // Add 20% buffer
            });

          const rewardsInOKB = web3.utils.fromWei(availableRewards, "gwei"); // OKB uses different decimals
          showSuccess(
            `Rewards claimed! ${rewardsInOKB} OKB received. TX: ${tx.transactionHash}`
          );

          // Update UI
          await updateBurnInfo();
        } catch (error) {
          console.error("Claim failed:", error);
          showError(`Claim failed: ${error.message}`);
        } finally {
          const claimButton = document.getElementById("claimRewardsButton");
          claimButton.disabled = false;
          claimButton.textContent = "Claim Rewards";
        }
      }

      // Debug function to check contract data
      async function debugContractData() {
        try {
          if (!connectedWalletAddress) {
            console.log("No wallet connected");
            return;
          }

          const web3 = new Web3(window.ethereum);
          const burnContract = new web3.eth.Contract(
            BURN_CONTRACT_ABI,
            BURN_CONTRACT_ADDRESS
          );
          const burnTokenContract = new web3.eth.Contract(
            ERC20_ABI,
            BURN_TOKEN_ADDRESS
          );

          console.log("=== Contract Debug Info ===");
          console.log("Wallet Address:", connectedWalletAddress);
          console.log("Burn Contract:", BURN_CONTRACT_ADDRESS);
          console.log("Burn Token:", BURN_TOKEN_ADDRESS);

          const userInfo = await burnContract.methods
            .getUserInfo(connectedWalletAddress)
            .call();
          console.log("User Info:", userInfo);

          const burnTokenBalance = await burnTokenContract.methods
            .balanceOf(connectedWalletAddress)
            .call();
          console.log(
            "Burn Token Balance:",
            web3.utils.fromWei(burnTokenBalance, "ether")
          );

          const availableRewards = await burnContract.methods
            .canRewards(connectedWalletAddress)
            .call();
          console.log(
            "Available Rewards (OKB):",
            web3.utils.fromWei(availableRewards, "gwei")
          );

          const totalBurn = await burnContract.methods.totalBurn().call();
          console.log(
            "Total Burn Global (burn):",
            web3.utils.fromWei(totalBurn, "ether")
          );

          const totalRewards = await burnContract.methods.totalRewards().call();
          console.log(
            "Total Rewards Global (burn):",
            web3.utils.fromWei(totalRewards, "ether")
          );

          const totalReceive = await burnContract.methods.totalReceive().call();
          console.log(
            "Total Received Global (OKB):",
            web3.utils.fromWei(totalReceive, "gwei")
          );
        } catch (error) {
          console.error("Debug error:", error);
        }
      }

      // Update burn information display
      async function updateBurnInfo() {
        try {
          if (!connectedWalletAddress) return;

          const web3 = new Web3(window.ethereum);
          const burnContract = new web3.eth.Contract(
            BURN_CONTRACT_ABI,
            BURN_CONTRACT_ADDRESS
          );

          // Debug contract data
          await debugContractData();

          // Get user info from contract
          const userInfo = await burnContract.methods
            .getUserInfo(connectedWalletAddress)
            .call();

          // Update burn page info - get burn token balance (the token user burns)
          const burnTokenContract = new web3.eth.Contract(
            ERC20_ABI,
            BURN_TOKEN_ADDRESS
          );
          const burnTokenBalance = await burnTokenContract.methods
            .balanceOf(connectedWalletAddress)
            .call();

          console.log("Burn Token Balance (raw):", burnTokenBalance.toString());
          console.log(
            "Burn Token Balance (formatted):",
            web3.utils.fromWei(burnTokenBalance, "ether")
          );

          // Get burn contract's OKB balance (pool remaining OKB)
          const poolOkbBalance = await web3.eth.getBalance(
            BURN_CONTRACT_ADDRESS
          );
          console.log("Pool OKB Balance (raw):", poolOkbBalance.toString());
          console.log(
            "Pool OKB Balance (formatted):",
            web3.utils.fromWei(poolOkbBalance, "ether")
          );
          const availableRewards = await burnContract.methods
            .canRewards(connectedWalletAddress)
            .call();

          // Get global stats
          const totalBurnGlobal = await burnContract.methods.totalBurn().call();
          const totalRewardsGlobal = await burnContract.methods
            .totalRewards()
            .call();
          const totalReceiveGlobal = await burnContract.methods
            .totalReceive()
            .call();

          // Convert from wei to appropriate units
          const burnTokenBalanceEth = web3.utils.fromWei(
            burnTokenBalance,
            "ether"
          );
          const availableRewardsOKB = web3.utils.fromWei(
            availableRewards,
            "gwei"
          ); // OKB rewards use gwei precision
          // Debug user staking value
          console.log("=== USER TOTAL BURNED DEBUG ===");
          console.log("User staking (raw):", userInfo.staking.toString());
          console.log(
            "User staking (ether):",
            web3.utils.fromWei(userInfo.staking, "ether")
          );
          console.log(
            "User staking (gwei):",
            web3.utils.fromWei(userInfo.staking, "gwei")
          );
          console.log(
            "User staking (finney):",
            web3.utils.fromWei(userInfo.staking, "finney")
          );
          console.log(
            "User staking (mwei):",
            web3.utils.fromWei(userInfo.staking, "mwei")
          );

          // Also debug global total burn
          console.log("=== GLOBAL TOTAL BURNED DEBUG ===");
          console.log("Global total burn (raw):", totalBurnGlobal.toString());
          console.log(
            "Global total burn (ether):",
            web3.utils.fromWei(totalBurnGlobal, "ether")
          );
          console.log(
            "Global total burn (gwei):",
            web3.utils.fromWei(totalBurnGlobal, "gwei")
          );
          console.log(
            "Global total burn (finney):",
            web3.utils.fromWei(totalBurnGlobal, "finney")
          );
          console.log(
            "Global total burn (mwei):",
            web3.utils.fromWei(totalBurnGlobal, "mwei")
          );

          // Based on debug info: user staking raw value 408082604 should use mwei precision
          // to get 408.082604 which is the correct burned amount
          const userTotalBurnedEth = web3.utils.fromWei(
            userInfo.staking,
            "mwei"
          ); // Use mwei precision for user burned amount
          const historyRewardsEth = web3.utils.fromWei(
            userInfo.historyRewards,
            "gwei"
          );

          // Update UI elements
          const burnBalanceEl = document.getElementById("burnBalance");
          const poolOkbBalanceEl = document.getElementById("poolOkbBalance");

          if (burnBalanceEl) {
            burnBalanceEl.textContent =
              parseFloat(burnTokenBalanceEth).toFixed(6);
            console.log("Updated burn Balance:", burnBalanceEl.textContent);
          }

          // Update pool OKB balance
          if (poolOkbBalanceEl) {
            const poolOkbFormatted = web3.utils.fromWei(
              poolOkbBalance,
              "ether"
            );
            poolOkbBalanceEl.textContent =
              parseFloat(poolOkbFormatted).toFixed(6);
            console.log(
              "Updated Pool OKB Balance:",
              poolOkbBalanceEl.textContent
            );
          }

          // Update current rewards amount from rewards page available rewards
          const currentRewardsAmountEl = document.getElementById(
            "currentRewardsAmount"
          );
          if (currentRewardsAmountEl) {
            // Use available rewards from rewards page
            const availableRewardsGlobalEl = document.getElementById(
              "availableRewardsGlobal"
            );
            if (availableRewardsGlobalEl) {
              const availableRewardsText = availableRewardsGlobalEl.textContent;
              currentRewardsAmountEl.textContent =
                parseFloat(availableRewardsText).toFixed(3);
            } else {
              currentRewardsAmountEl.textContent = "0.000";
            }
          }

          // Update rewards page statistics
          // Check if the values need different precision conversion
          console.log("Raw contract values:", {
            totalBurnGlobal: totalBurnGlobal.toString(),
            totalRewardsGlobal: totalRewardsGlobal.toString(),
            totalReceiveGlobal: totalReceiveGlobal.toString(),
          });

          // Try different precision conversions based on actual contract implementation
          // totalBurn() and totalRewards() might use gwei precision (9 decimals) like OKB
          const totalBurnGlobalDisplay = web3.utils.fromWei(
            totalBurnGlobal,
            "gwei"
          );
          const totalRewardsGlobalDisplay = web3.utils.fromWei(
            totalRewardsGlobal,
            "gwei"
          );
          // totalReceive() returns total claimed rewards in OKB equivalent (9 decimals)
          const totalReceiveGlobalOKB = web3.utils.fromWei(
            totalReceiveGlobal,
            "gwei"
          );

          console.log("Converted values:", {
            totalBurnDisplay: totalBurnGlobalDisplay,
            totalRewardsDisplay: totalRewardsGlobalDisplay,
            totalReceiveOKB: totalReceiveGlobalOKB,
          });

          // Update the three main stats in rewards page using specific IDs
          const totalBurnedGlobalEl =
            document.getElementById("totalBurnedGlobal");
          const totalRewardsGlobalEl =
            document.getElementById("totalRewardsGlobal");
          const availableRewardsGlobalEl = document.getElementById(
            "availableRewardsGlobal"
          );

          console.log("Found global stat elements:", {
            totalBurnedGlobal: !!totalBurnedGlobalEl,
            totalRewardsGlobal: !!totalRewardsGlobalEl,
            availableRewardsGlobal: !!availableRewardsGlobalEl,
          });

          if (totalBurnedGlobalEl) {
            totalBurnedGlobalEl.textContent = parseFloat(
              totalBurnGlobalDisplay
            ).toFixed(8);
            console.log(
              "Updated Global Total Burned:",
              totalBurnedGlobalEl.textContent
            );
          }

          if (totalRewardsGlobalEl) {
            totalRewardsGlobalEl.textContent = parseFloat(
              totalRewardsGlobalDisplay
            ).toFixed(8);
            console.log(
              "Updated Global Total Rewards:",
              totalRewardsGlobalEl.textContent
            );
          }

          if (availableRewardsGlobalEl) {
            availableRewardsGlobalEl.textContent =
              parseFloat(availableRewardsOKB).toFixed(8);
            console.log(
              "Updated Global Available Rewards:",
              availableRewardsGlobalEl.textContent
            );
          }

          // Also update the rewards display in the bottom section
          const rewardsElements = document.querySelectorAll(".info-value");
          if (rewardsElements.length >= 3) {
            // Find elements by their text content context
            rewardsElements.forEach((el, index) => {
              const parent = el.parentElement;
              const label = parent.querySelector(".info-label");
              if (label) {
                const labelText = label.textContent.trim();
                if (labelText.includes("Total Burned")) {
                  el.textContent = parseFloat(totalBurnGlobalDisplay).toFixed(
                    8
                  );
                } else if (labelText.includes("Total Rewards")) {
                  el.textContent = parseFloat(
                    totalRewardsGlobalDisplay
                  ).toFixed(8);
                } else if (
                  labelText.includes("Rewards") &&
                  !labelText.includes("Total")
                ) {
                  el.textContent =
                    parseFloat(availableRewardsOKB).toFixed(8) + " OKB";
                }
              }
            });
          }
        } catch (error) {
          console.error("Error updating burn info:", error);
        }
      }

      // Copy burn invite link function
      function copyBurnLink() {
        const linkInput = document.getElementById("burnInviteLink");
        if (linkInput) {
          linkInput.select();
          linkInput.setSelectionRange(0, 99999); // For mobile devices

          try {
            document.execCommand("copy");
            // Show success feedback
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = "Copied!";
            button.style.background =
              "linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)";

            setTimeout(() => {
              button.textContent = originalText;
              button.style.background =
                "linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)";
            }, 2000);
          } catch (err) {
            console.error("Failed to copy: ", err);
            // Fallback for modern browsers
            if (navigator.clipboard) {
              navigator.clipboard.writeText(linkInput.value).then(() => {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = "Copied!";
                setTimeout(() => {
                  button.textContent = originalText;
                }, 2000);
              });
            }
          }
        }
      }
      let userPurchased = 0;
      let connectedWalletAddress = "";
      let referrerAddress = "";

      const MIN_AMOUNT = 0.1;
      const MAX_AMOUNT = 2.0;
      const TOTAL_SUPPLY = 300;
      const PROGRESS_WALLET = "******************************************";
      const DEFAULT_REFERRER = "******************************************";
      const bskTokenAddress = "******************************************";
      const bskTokenABI = [
        {
          inputs: [
            { internalType: "address", name: "parent", type: "address" },
          ],
          name: "presale",
          outputs: [],
          stateMutability: "payable",
          type: "function",
        },
        {
          inputs: [],
          name: "saleActive",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "walletSpent",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [],
          name: "totalEthReceived",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          stateMutability: "view",
          type: "function",
        },
      ];

      // Burn Contract ABI (Complete)
      const BURN_CONTRACT_ABI = [
        {
          constant: false,
          inputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "constructor",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "owner",
              type: "address",
            },
            {
              indexed: true,
              internalType: "address",
              name: "spender",
              type: "address",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "value",
              type: "uint256",
            },
          ],
          name: "Approval",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "account",
              type: "address",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "amount",
              type: "uint256",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "totalRewards",
              type: "uint256",
            },
          ],
          name: "ArriveFeeRewards",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "account",
              type: "address",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "amount",
              type: "uint256",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "totalBurn",
              type: "uint256",
            },
          ],
          name: "IncreaseStaking",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "previousOwner",
              type: "address",
            },
            {
              indexed: true,
              internalType: "address",
              name: "newOwner",
              type: "address",
            },
          ],
          name: "OwnershipTransferred",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "account",
              type: "address",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "amount",
              type: "uint256",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "totalReceive",
              type: "uint256",
            },
          ],
          name: "ReceiveReward",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [
            {
              indexed: true,
              internalType: "address",
              name: "from",
              type: "address",
            },
            {
              indexed: true,
              internalType: "address",
              name: "to",
              type: "address",
            },
            {
              indexed: false,
              internalType: "uint256",
              name: "value",
              type: "uint256",
            },
          ],
          name: "Transfer",
          payable: false,
          type: "event",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "Invitation",
          outputs: [{ internalType: "address", name: "", type: "address" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "Rewards",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "Share",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "ShareTokens",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "_burnToken",
          outputs: [
            {
              internalType: "contract burnToken",
              name: "",
              type: "address",
            },
          ],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "_liquidityFee",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "_taxFee",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "owner", type: "address" },
            { internalType: "address", name: "spender", type: "address" },
          ],
          name: "allowance",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "spender", type: "address" },
            { internalType: "uint256", name: "amount", type: "uint256" },
          ],
          name: "approve",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "arriveFeeRewards",
          outputs: [],
          payable: false,
          stateMutability: "payable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            {
              internalType: "uint256",
              name: "increase",
              type: "uint256",
            },
          ],
          name: "arriveRewardsAdmin",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "balanceOf",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "burnAmount",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "amount", type: "uint256" },
            {
              internalType: "address",
              name: "_invitation",
              type: "address",
            },
          ],
          name: "burnToHolder",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "addr", type: "address" }],
          name: "canRewards",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "decimals",
          outputs: [{ internalType: "uint8", name: "", type: "uint8" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "spender", type: "address" },
            {
              internalType: "uint256",
              name: "subtractedValue",
              type: "uint256",
            },
          ],
          name: "decreaseAllowance",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "errorBalance",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "_token", type: "address" },
          ],
          name: "errorToken",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "excludeFromFee",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "excludeFromReward",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "getInvitationLength",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "from", type: "uint256" },
            { internalType: "uint256", name: "limit", type: "uint256" },
          ],
          name: "getInvitationList",
          outputs: [
            {
              components: [
                { name: "addr", type: "address" },
                { name: "staking", type: "uint256" },
                { name: "rewards", type: "uint256" },
                { name: "receives", type: "uint256" },
                { name: "share", type: "uint256" },
                { name: "invitation", type: "address" },
                { name: "historyRewards", type: "uint256" },
                { name: "shareTokens", type: "uint256" },
              ],
              internalType: "struct burnBuild.userInfo[]",
              name: "items",
              type: "tuple[]",
            },
          ],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "getTime",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "getUnlockTime",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "addr", type: "address" }],
          name: "getUserInfo",
          outputs: [
            {
              components: [
                { name: "addr", type: "address" },
                { name: "staking", type: "uint256" },
                { name: "rewards", type: "uint256" },
                { name: "receives", type: "uint256" },
                { name: "share", type: "uint256" },
                { name: "invitation", type: "address" },
                { name: "historyRewards", type: "uint256" },
                { name: "shareTokens", type: "uint256" },
              ],
              internalType: "struct burnBuild.userInfo",
              name: "info",
              type: "tuple",
            },
          ],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "getUserLength",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "from", type: "uint256" },
            { internalType: "uint256", name: "limit", type: "uint256" },
          ],
          name: "getUserList",
          outputs: [
            {
              components: [
                { name: "addr", type: "address" },
                { name: "staking", type: "uint256" },
                { name: "rewards", type: "uint256" },
                { name: "receives", type: "uint256" },
                { name: "share", type: "uint256" },
                { name: "invitation", type: "address" },
                { name: "historyRewards", type: "uint256" },
                { name: "shareTokens", type: "uint256" },
              ],
              internalType: "struct burnBuild.userInfo[]",
              name: "items",
              type: "tuple[]",
            },
          ],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "historyRewards",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "includeInFee",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "spender", type: "address" },
            {
              internalType: "uint256",
              name: "addedValue",
              type: "uint256",
            },
          ],
          name: "increaseAllowance",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "invFee",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "isExcludedFromFee",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "account", type: "address" },
          ],
          name: "isExcludedFromReward",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "uint256", name: "time", type: "uint256" }],
          name: "lock",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "name",
          outputs: [{ internalType: "string", name: "", type: "string" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "owner",
          outputs: [{ internalType: "address", name: "", type: "address" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            {
              internalType: "address payable",
              name: "to",
              type: "address",
            },
          ],
          name: "receiveRewards",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "tAmount", type: "uint256" },
            {
              internalType: "bool",
              name: "deductTransferFee",
              type: "bool",
            },
          ],
          name: "reflectionFromToken",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "renounceOwnership",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "from", type: "address" }],
          name: "setInvitation",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            {
              internalType: "uint256",
              name: "liquidityFee",
              type: "uint256",
            },
          ],
          name: "setLiquidityFeePercent",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "addr", type: "address" }],
          name: "setMainToken",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "taxFee", type: "uint256" },
          ],
          name: "setTaxFeePercent",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "symbol",
          outputs: [{ internalType: "string", name: "", type: "string" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "rAmount", type: "uint256" },
          ],
          name: "tokenFromReflection",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "totalBurn",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "totalReceive",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "totalRewards",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "totalSupply",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            {
              internalType: "address",
              name: "recipient",
              type: "address",
            },
            { internalType: "uint256", name: "amount", type: "uint256" },
          ],
          name: "transfer",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "address", name: "sender", type: "address" },
            {
              internalType: "address",
              name: "recipient",
              type: "address",
            },
            { internalType: "uint256", name: "amount", type: "uint256" },
          ],
          name: "transferFrom",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            {
              internalType: "address",
              name: "newOwner",
              type: "address",
            },
          ],
          name: "transferOwnership",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "uniswapRouter",
          outputs: [
            {
              internalType: "contract IUniswapV2Router02",
              name: "",
              type: "address",
            },
          ],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [],
          name: "unlock",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "vipUser",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          payable: false,
          stateMutability: "view",
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { internalType: "uint256", name: "amount", type: "uint256" },
          ],
          name: "withdrawOKB",
          outputs: [],
          payable: false,
          stateMutability: "nonpayable",
          type: "function",
        },
        {
          constant: false,
          payable: false,
          stateMutability: "payable",
          type: "receive",
        },
      ];

      const BURN_CONTRACT_ADDRESS =
        "0xeE36531E61298ABBCF9147d58F7Faa5D3762fa0b"; //燃烧合约
      const BURN_TOKEN_ADDRESS = "******************************************"; //代币合约
      const SWAP_ROUTER_ADDRESS = "0x881fb2f98c13d521009464e7d1cbf16e1b394e8e";

      // ERC20 Token ABI for burn token
      const ERC20_ABI = [
        {
          constant: true,
          inputs: [{ name: "_owner", type: "address" }],
          name: "balanceOf",
          outputs: [{ name: "balance", type: "uint256" }],
          type: "function",
        },
        {
          constant: false,
          inputs: [
            { name: "_spender", type: "address" },
            { name: "_value", type: "uint256" },
          ],
          name: "approve",
          outputs: [{ name: "", type: "bool" }],
          type: "function",
        },
        {
          constant: true,
          inputs: [
            { name: "_owner", type: "address" },
            { name: "_spender", type: "address" },
          ],
          name: "allowance",
          outputs: [{ name: "", type: "uint256" }],
          type: "function",
        },
        {
          constant: true,
          inputs: [],
          name: "decimals",
          outputs: [{ name: "", type: "uint8" }],
          type: "function",
        },
      ];

      const OKB_CHAIN_ID = "0xc4";
      const OKB_CHAIN_CONFIG = {
        chainId: OKB_CHAIN_ID,
        chainName: "X Layer Mainnet",
        nativeCurrency: {
          name: "OKB",
          symbol: "OKB",
          decimals: 18,
        },
        rpcUrls: ["https://rpc.xlayer.tech"],
        blockExplorerUrls: ["https://www.okx.com/web3/explorer/xlayer"],
      };

      function getMainnetConfig() {
        return {
          chainId: "0xc4",
          chainName: "X Layer Mainnet",
          nativeCurrency: {
            name: "OKB",
            symbol: "OKB",
            decimals: 18,
          },
          rpcUrls: ["https://rpc.xlayer.tech"],
          blockExplorerUrls: ["https://www.okx.com/web3/explorer/xlayer"],
        };
      }

      async function forceMainnetSwitch() {
        try {
          console.log("Force switching to X Layer Mainnet (196)...");

          await window.ethereum.request({
            method: "wallet_switchEthereumChain",
            params: [{ chainId: "0xc4" }],
          });

          return true;
        } catch (error) {
          console.log("Direct switch failed, trying to add X Layer Mainnet...");
          try {
            await window.ethereum.request({
              method: "wallet_addEthereumChain",
              params: [getMainnetConfig()],
            });
            return true;
          } catch (addError) {
            console.error("Failed to add X Layer Mainnet:", addError);
            return false;
          }
        }
      }

      function updateDisplay() {
        if (totalSold < 79.1) {
          totalSold = 79.1;
        }

        document.getElementById("amountDisplay").textContent =
          currentAmount.toFixed(1);
        document.getElementById("buyBtn").disabled = !walletConnected;

        // Limit display values to maximum of 300 OKB and 100%
        const displaySold = Math.min(totalSold, TOTAL_SUPPLY);
        const progress = Math.min((totalSold / TOTAL_SUPPLY) * 100, 100);

        // Ensure all display values are capped at maximum limits
        const cappedProgress = Math.min(progress, 100);
        const cappedDisplaySold = Math.min(displaySold, 300);

        document.getElementById("progressFill").style.width =
          cappedProgress + "%";
        document.getElementById(
          "progressValue"
        ).textContent = `${cappedDisplaySold.toFixed(1)} / ${TOTAL_SUPPLY} OKB`;
        document.getElementById("progressPercentage").textContent =
          cappedProgress.toFixed(1) + "%";
      }

      function increaseAmount() {
        if (currentAmount < MAX_AMOUNT) {
          currentAmount = Math.min(currentAmount + 0.1, MAX_AMOUNT);
          currentAmount = Math.round(currentAmount * 10) / 10;
          updateDisplay();
        }
      }

      function decreaseAmount() {
        if (currentAmount > MIN_AMOUNT) {
          currentAmount = Math.max(currentAmount - 0.1, MIN_AMOUNT);
          currentAmount = Math.round(currentAmount * 10) / 10;
          updateDisplay();
        }
      }

      function setMaxAmount() {
        currentAmount = MAX_AMOUNT;
        updateDisplay();
      }

      async function switchToOKBChain() {
        try {
          console.log(
            "Attempting to switch to X Layer Mainnet (Chain ID: 196)"
          );

          await window.ethereum.request({
            method: "wallet_switchEthereumChain",
            params: [{ chainId: "0xc4" }],
          });

          console.log("Successfully switched to X Layer Mainnet");
          return true;
        } catch (switchError) {
          console.log("Switch error:", switchError);

          if (switchError.code === 4902) {
            try {
              console.log("Adding X Layer Mainnet to MetaMask...");
              const mainnetConfig = getMainnetConfig();
              console.log("X Layer Mainnet config:", mainnetConfig);

              await window.ethereum.request({
                method: "wallet_addEthereumChain",
                params: [mainnetConfig],
              });

              console.log("Successfully added and switched to X Layer Mainnet");
              return true;
            } catch (addError) {
              console.error("Failed to add X Layer Mainnet:", addError);
              return false;
            }
          } else {
            console.error("Failed to switch to X Layer Mainnet:", switchError);
            return false;
          }
        }
      }

      async function getCurrentChainId() {
        try {
          const chainId = await window.ethereum.request({
            method: "eth_chainId",
          });
          return chainId;
        } catch (error) {
          console.error("Failed to get chain ID:", error);
          return null;
        }
      }

      function isCorrectChain(currentChainId) {
        if (!currentChainId) return false;
        const normalizedCurrent = currentChainId.toLowerCase();
        const normalizedExpected = OKB_CHAIN_ID.toLowerCase();
        const decimalCurrent = parseInt(normalizedCurrent, 16);
        const decimalExpected = parseInt(normalizedExpected, 16);

        console.log("Chain comparison:", {
          current: normalizedCurrent,
          expected: normalizedExpected,
          currentDecimal: decimalCurrent,
          expectedDecimal: decimalExpected,
        });

        return (
          normalizedCurrent === normalizedExpected ||
          decimalCurrent === decimalExpected
        );
      }

      function getPurchasedAmount(walletAddress) {
        if (!walletAddress) return 0;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        const stored = localStorage.getItem(key);
        return stored ? parseFloat(stored) : 0;
      }

      function savePurchasedAmount(walletAddress, amount) {
        if (!walletAddress) return;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        const currentAmount = getPurchasedAmount(walletAddress);
        const newAmount = currentAmount + amount;
        localStorage.setItem(key, newAmount.toString());
        return newAmount;
      }

      function updatePurchasedDisplay() {
        if (connectedWalletAddress) {
          userPurchased = getPurchasedAmount(connectedWalletAddress);
          // purchasedAmount element removed, no need to update display
        }
      }

      function clearPurchaseHistory(walletAddress) {
        if (!walletAddress) return;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        localStorage.removeItem(key);
        updatePurchasedDisplay();
        console.log("Purchase history cleared for:", walletAddress);
      }
      function showToast(message, type = "default", duration = 3000) {
        const existingToast = document.querySelector(".toast");
        if (existingToast) {
          existingToast.classList.remove("show");
          setTimeout(() => {
            if (existingToast.parentNode) {
              existingToast.parentNode.removeChild(existingToast);
            }
          }, 300);
        }

        const toast = document.createElement("div");
        toast.className = `toast ${type}`;
        toast.textContent = message;

        toast.style.transform = "translate(-50%, -50%) scale(0.8)";

        document.body.appendChild(toast);

        setTimeout(() => {
          toast.classList.add("show");
        }, 50);
        setTimeout(() => {
          toast.classList.remove("show");
          toast.style.transform = "translate(-50%, -50%) scale(0.8)";
          setTimeout(() => {
            if (toast.parentNode) {
              toast.parentNode.removeChild(toast);
            }
          }, 300);
        }, duration);
      }

      function showSuccess(message, duration = 3000) {
        showToast(message, "success", duration);
      }

      function showError(message, duration = 3000) {
        showToast(message, "error", duration);
      }

      function showWarning(message, duration = 3000) {
        showToast(message, "warning", duration);
      }
      function getReferrerFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const referrer = urlParams.get("a");
        if (referrer && referrer.match(/^0x[a-fA-F0-9]{40}$/)) {
          return referrer;
        }
        return "";
      }

      function generateInviteLink(walletAddress) {
        if (!walletAddress) return "";
        const baseUrl = window.location.origin + window.location.pathname;
        return `${baseUrl}?a=${walletAddress}`;
      }
      function updateInviteLink() {
        const inviteLinkInput = document.getElementById("inviteLink");
        const copyBtn = document.getElementById("copyBtn");
        const burnInviteLinkInput = document.getElementById("burnInviteLink");

        if (connectedWalletAddress) {
          const inviteLink = generateInviteLink(connectedWalletAddress);
          inviteLinkInput.value = inviteLink;
          copyBtn.disabled = false;

          // Update burn invite link
          if (burnInviteLinkInput) {
            const burnInviteLink = `https://burn.fun/?ref=${connectedWalletAddress}`;
            burnInviteLinkInput.value = burnInviteLink;
          }
        } else {
          inviteLinkInput.value = "";
          const placeholderText =
            currentLanguage === "zh"
              ? "连接钱包以生成推荐链接"
              : "Connect wallet to generate referral link";
          inviteLinkInput.placeholder = placeholderText;
          copyBtn.disabled = true;

          // Reset burn invite link
          if (burnInviteLinkInput) {
            burnInviteLinkInput.value = "";
          }
        }
      }

      function copyInviteLink() {
        const inviteLinkInput = document.getElementById("inviteLink");
        if (inviteLinkInput.value) {
          inviteLinkInput.select();
          inviteLinkInput.setSelectionRange(0, 99999);

          try {
            document.execCommand("copy");
            showSuccess("Referral link copied to clipboard!", 2000);
          } catch (err) {
            navigator.clipboard
              .writeText(inviteLinkInput.value)
              .then(() => {
                showSuccess("Referral link copied to clipboard!", 2000);
              })
              .catch(() => {
                showError("Copy failed, please copy manually", 2000);
              });
          }
        }
      }

      async function ensureMainnetConnection() {
        try {
          const currentChainId = await getCurrentChainId();
          console.log(
            "Checking chain connection. Current:",
            currentChainId,
            "Expected: 0xc4 (196 - X Layer Mainnet)"
          );

          if (currentChainId !== "0xc4") {
            showToast("Switching to X Layer Mainnet...", "default", 2000);

            const switched = await forceMainnetSwitch();
            if (switched) {
              await new Promise((resolve) => setTimeout(resolve, 1000));
              const newChainId = await getCurrentChainId();
              if (newChainId === "0xc4") {
                showSuccess("Successfully switched to X Layer Mainnet!", 2000);
              } else {
                throw new Error(
                  `Switch failed. Current: ${newChainId}, Expected: 0xc4 (X Layer Mainnet)`
                );
              }
            } else {
              throw new Error("Failed to switch to X Layer Mainnet");
            }
          } else {
            console.log("Already on X Layer Mainnet");
          }
        } catch (error) {
          console.error("Error ensuring X Layer Mainnet connection:", error);
          showWarning(
            "Please manually switch to X Layer Mainnet (Chain ID: 196)",
            4000
          );
          throw error;
        }
      }

      async function checkAndSwitchToMainnet() {
        try {
          const currentChainId = await getCurrentChainId();
          if (currentChainId && !isCorrectChain(currentChainId)) {
            console.log(
              "Detected non-X Layer Mainnet chain, attempting to switch..."
            );
            showToast(
              "Detected wrong network. Switching to X Layer Mainnet...",
              "warning",
              3000
            );

            const switched = await switchToOKBChain();
            if (switched) {
              showSuccess("Switched to X Layer Mainnet!", 2000);
            }
          }
        } catch (error) {
          console.log(
            "Could not auto-switch chain (wallet may not be connected)"
          );
        }
      }

      async function connectWallet() {
        if (typeof window.ethereum !== "undefined") {
          try {
            const accounts = await window.ethereum.request({
              method: "eth_requestAccounts",
            });

            await ensureMainnetConnection();
            const currentChainId = await getCurrentChainId();
            console.log(
              "Current Chain ID:",
              currentChainId,
              "Expected:",
              OKB_CHAIN_ID,
              "(X Layer Mainnet)"
            );
            if (!isCorrectChain(currentChainId)) {
              showError(
                "Failed to switch to X Layer Mainnet. Please switch manually."
              );
              return;
            }

            const account = accounts[0];
            walletConnected = true;
            connectedWalletAddress = account;

            const shortAddress =
              account.slice(0, 6) + "..." + account.slice(-4);
            document.getElementById("walletAddress").innerHTML = shortAddress;

            updatePurchasedDisplay();

            updateInviteLink();
            await updateBalance();
            await updateBurnInfo();
            await refreshQuotaInfo();
            updateDisplay();
          } catch (error) {
            console.error("Failed to connect wallet:", error);
            showError("Failed to connect wallet");
          }
        } else {
          showError("Please install MetaMask or another Web3 wallet");
        }
      }

      async function getWalletBalance(address) {
        try {
          if (typeof window.ethereum !== "undefined") {
            const currentChainId = await getCurrentChainId();
            if (!isCorrectChain(currentChainId)) {
              console.log("Not on X Layer Mainnet, switching...");
              const switched = await switchToOKBChain();
              if (!switched) {
                console.error("Failed to switch to X Layer Mainnet");
                return 0;
              }
            }

            const balance = await window.ethereum.request({
              method: "eth_getBalance",
              params: [address, "latest"],
            });

            const balanceInOKB = parseInt(balance, 16) / Math.pow(10, 18);
            return balanceInOKB;
          } else {
            return Math.random() * 50;
          }
        } catch (error) {
          console.error("Error getting wallet balance:", error);
          return Math.random() * 50;
        }
      }

      async function updateBalance() {
        try {
          if (walletConnected && window.ethereum) {
            const accounts = await window.ethereum.request({
              method: "eth_accounts",
            });
            if (accounts.length > 0) {
              userBalance = await getWalletBalance(accounts[0]);
            }
          }

          totalSold = await getWalletBalance(PROGRESS_WALLET);

          updatePurchasedDisplay();
        } catch (error) {
          console.error("Error updating balance:", error);
          userBalance = 0;
          totalSold = 0;
        }
      }

      async function buyTokens() {
        if (!walletConnected) {
          showWarning("Please connect your wallet first");
          return;
        }

        if (currentAmount < MIN_AMOUNT || currentAmount > MAX_AMOUNT) {
          showWarning(
            `Amount must be between ${MIN_AMOUNT} and ${MAX_AMOUNT} OKB`
          );
          return;
        }

        try {
          await ensureMainnetConnection();

          const finalChainId = await getCurrentChainId();
          console.log(
            "Final chain check before payment:",
            finalChainId,
            "(X Layer Mainnet)"
          );

          if (finalChainId !== "0xc4") {
            throw new Error(
              `Still on wrong network: ${finalChainId}, expected: 0xc4 (X Layer Mainnet)`
            );
          }

          showToast(
            "Confirmed on X Layer Mainnet. Processing payment...",
            "success",
            2000
          );
        } catch (error) {
          console.error("X Layer Mainnet verification failed:", error);
          showError(
            "Cannot proceed without X Layer Mainnet connection. Please switch manually."
          );
          return;
        }

        try {
          document.getElementById("buyBtn").textContent = "Processing...";
          document.getElementById("buyBtn").disabled = true;

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          const currentAccount = accounts[0];
          const currentBalance = await getWalletBalance(currentAccount);

          console.log(`Current account: ${currentAccount}`);
          console.log(`Current balance: ${currentBalance} OKB`);
          console.log(`Trying to buy: ${currentAmount} OKB`);

          const parentAddress = referrerAddress || DEFAULT_REFERRER;
          console.log(`Parent address: ${parentAddress}`);

          if (!window.ethereum) {
            throw new Error("MetaMask not detected");
          }

          const web3 = new Web3(window.ethereum);

          if (!web3.utils.isAddress(parentAddress)) {
            throw new Error(`Invalid parent address: ${parentAddress}`);
          }
          const contract = new web3.eth.Contract(bskTokenABI, bskTokenAddress);

          const amountInWei = web3.utils.toWei(
            currentAmount.toString(),
            "ether"
          );
          console.log(`Converting ${currentAmount} OKB to ${amountInWei} wei`);

          console.log(`Contract address: ${bskTokenAddress}`);
          console.log(
            `Amount in wei: ${amountInWei}, Parent: ${parentAddress}`
          );
          console.log(`Current balance: ${currentBalance} OKB`);
          console.log(`Required amount: ${currentAmount} OKB`);

          const contractCode = await web3.eth.getCode(bskTokenAddress);
          if (contractCode === "0x") {
            throw new Error("Contract not found at the specified address");
          }
          console.log("Contract exists, proceeding with transaction...");

          const currentChainId = await web3.eth.getChainId();
          console.log(`Current chain ID: ${currentChainId} (should be 196)`);

          if (currentChainId !== 196) {
            throw new Error(
              `Wrong network! Current: ${currentChainId}, Expected: 196 (X Layer Mainnet)`
            );
          }

          if (currentBalance < currentAmount) {
            throw new Error(
              `Insufficient balance. You have ${currentBalance.toFixed(
                3
              )} OKB but need ${currentAmount} OKB`
            );
          }

          try {
            const saleActive = await contract.methods.saleActive().call();
            console.log(`Sale active: ${saleActive}`);

            if (!saleActive) {
              throw new Error("Sale is not active");
            }
          } catch (readError) {
            console.log("Cannot read sale status:", readError.message);
          }

          try {
            const walletSpent = await contract.methods
              .walletSpent(currentAccount)
              .call();
            console.log(
              `Wallet already spent: ${web3.utils.fromWei(
                walletSpent,
                "ether"
              )} OKB`
            );

            const totalEthReceived = await contract.methods
              .totalEthReceived()
              .call();
            console.log(
              `Total ETH received by contract: ${web3.utils.fromWei(
                totalEthReceived,
                "ether"
              )} OKB`
            );
          } catch (readError) {
            console.log("Cannot read wallet/contract data:", readError.message);
          }

          try {
            const minPurchase = await contract.methods.MIN_PURCHASE().call();
            const maxPurchase = await contract.methods.MAX_PURCHASE().call();
            console.log(
              `Min purchase: ${web3.utils.fromWei(minPurchase, "ether")} OKB`
            );
            console.log(
              `Max purchase: ${web3.utils.fromWei(maxPurchase, "ether")} OKB`
            );

            if (web3.utils.toBN(amountInWei).lt(web3.utils.toBN(minPurchase))) {
              throw new Error(
                `Purchase amount too small. Minimum: ${web3.utils.fromWei(
                  minPurchase,
                  "ether"
                )} OKB`
              );
            }

            if (web3.utils.toBN(amountInWei).gt(web3.utils.toBN(maxPurchase))) {
              throw new Error(
                `Purchase amount too large. Maximum: ${web3.utils.fromWei(
                  maxPurchase,
                  "ether"
                )} OKB`
              );
            }
          } catch (limitError) {
            console.log("Cannot read purchase limits:", limitError.message);
          }

          console.log(`Calling buyTokens with parent: ${parentAddress}`);
          console.log(
            `Value being sent: ${amountInWei} wei (${currentAmount} OKB)`
          );
          console.log(`Account balance: ${currentBalance} OKB`);
          console.log(`Network: X Layer Mainnet (196)`);

          const gasEstimate = await contract.methods
            .presale(parentAddress)
            .estimateGas({
              from: currentAccount,
              value: amountInWei,
            });

          console.log(`Gas estimate successful: ${gasEstimate}`);

          const txHash = await contract.methods.presale(parentAddress).send({
            from: currentAccount,
            value: amountInWei,
            gas: Math.floor(gasEstimate * 1.2),
          });

          console.log("Contract transaction sent:", txHash.transactionHash);

          const finalTxHash = txHash.transactionHash || txHash;
          showSuccess(
            `Transaction sent!\nHash: ${finalTxHash}\nPlease wait for confirmation.`,
            4000
          );

          const newTotalPurchased = savePurchasedAmount(
            connectedWalletAddress,
            currentAmount
          );
          console.log(
            `Saved purchase: ${currentAmount} OKB, Total: ${newTotalPurchased} OKB`
          );

          updatePurchasedDisplay();
          await updateBalance();

          document.getElementById("buyBtn").textContent = "Buy";
          document.getElementById("buyBtn").disabled = false;
          updateDisplay();

          showSuccess(
            `Successfully purchased ${currentAmount} OKB!\nTransaction: ${finalTxHash}\nReferrer: ${parentAddress}\nTotal purchased: ${newTotalPurchased.toFixed(
              1
            )} OKB`,
            5000
          );
        } catch (error) {
          console.error("Transaction failed:", error);
          console.error("Error details:", {
            code: error.code,
            message: error.message,
            data: error.data,
          });

          let errorMessage = "Transaction failed";
          if (error.code === 4001) {
            errorMessage = "Transaction rejected by user";
          } else if (error.code === -32000) {
            errorMessage =
              "Contract execution failed. Please check: 1) Sufficient balance 2) Contract parameters 3) Network connection";
          } else if (error.code === -32603) {
            errorMessage = "Insufficient funds or network error";
          } else if (
            error.message &&
            error.message.includes("insufficient funds")
          ) {
            errorMessage = `Insufficient funds. You need at least ${currentAmount} OKB + gas fees`;
          } else if (error.message) {
            errorMessage = `Transaction failed: ${error.message}`;
          }

          showError(errorMessage, 5000);
          document.getElementById("buyBtn").textContent = "Buy";
          document.getElementById("buyBtn").disabled = false;
        }
      }

      updateDisplay();
      updatePurchasedDisplay();

      referrerAddress = getReferrerFromURL();
      if (referrerAddress) {
        console.log("Referrer found in URL:", referrerAddress);
        showSuccess(
          `Accessed via referral link, referrer: ${referrerAddress.slice(
            0,
            6
          )}...${referrerAddress.slice(-4)}`,
          3000
        );
      }

      updateBalance();
      if (typeof window.ethereum !== "undefined") {
        window.ethereum.request({ method: "eth_accounts" }).then((accounts) => {
          if (accounts.length > 0) {
            connectWallet();
          } else {
            checkAndSwitchToMainnet();
          }
        });
      }

      setInterval(updateBalance, 30000);
      if (typeof window.ethereum !== "undefined") {
        window.ethereum.on("chainChanged", (chainId) => {
          console.log("Chain changed to:", chainId);

          if (!isCorrectChain(chainId)) {
            showWarning(
              "Wrong network detected! Please use X Layer Mainnet (Chain ID: 196).",
              4000
            );
            setTimeout(() => {
              checkAndSwitchToMainnet();
            }, 2000);
          } else {
            showSuccess("Connected to X Layer Mainnet!", 2000);
            if (walletConnected) {
              updateBalance();
            }
          }
        });

        window.ethereum.on("accountsChanged", (accounts) => {
          if (accounts.length === 0) {
            walletConnected = false;
            connectedWalletAddress = "";
            const connectButtonText =
              currentLanguage === "zh" ? "连接钱包" : "Connect Wallet";
            document.getElementById(
              "walletAddress"
            ).innerHTML = `<button class="connect-wallet" onclick="connectWallet()"><span data-en="Connect Wallet" data-zh="连接钱包">${connectButtonText}</span></button>`;

            updatePurchasedDisplay();
            updateInviteLink();
          } else if (accounts[0] !== connectedWalletAddress) {
            connectWallet();
          }
        });
      }

      // Initialize the page with presale tab as default
      document.addEventListener("DOMContentLoaded", function () {
        switchTab("presale");

        // Initialize current rewards amount display
        const currentRewardsAmountEl = document.getElementById(
          "currentRewardsAmount"
        );
        if (currentRewardsAmountEl) {
          currentRewardsAmountEl.textContent = "0.000";
        }
      });
    </script>
  </body>
</html>
